<!DOCTYPE HTML>
<html lang="en">
<head>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta charset="UTF-8">
  <title>Rebooting - HUB75 Display</title>
  <style>
    body {
      background-color: #121212;
      color: white;
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      user-select: none;
    }

    .reboot-container {
      background-color: #333;
      border-radius: 15px;
      padding: 40px;
      text-align: center;
      box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      max-width: 500px;
      width: 100%;
    }

    .reboot-icon {
      font-size: 4em;
      margin-bottom: 20px;
      color: #FFD65C;
      animation: spin 2s linear infinite;
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    h1 {
      color: #FFD65C;
      margin-bottom: 20px;
      font-size: 2.2em;
    }

    .message {
      color: #ccc;
      margin-bottom: 30px;
      font-size: 1.2em;
      line-height: 1.5;
    }

    .countdown-container {
      background-color: #444;
      border-radius: 10px;
      padding: 20px;
      margin: 20px 0;
    }

    .countdown-label {
      color: #aaa;
      font-size: 14px;
      margin-bottom: 10px;
    }

    .countdown-number {
      font-size: 3em;
      font-weight: bold;
      color: #FFD65C;
      margin: 10px 0;
    }

    .progress-ring {
      width: 120px;
      height: 120px;
      margin: 20px auto;
    }

    .progress-ring__circle {
      stroke: #444;
      stroke-width: 8;
      fill: transparent;
      r: 52;
      cx: 60;
      cy: 60;
    }

    .progress-ring__progress {
      stroke: #FFD65C;
      stroke-width: 8;
      stroke-linecap: round;
      fill: transparent;
      r: 52;
      cx: 60;
      cy: 60;
      stroke-dasharray: 326.73;
      stroke-dashoffset: 326.73;
      transform: rotate(-90deg);
      transform-origin: 50% 50%;
      transition: stroke-dashoffset 1s linear;
    }

    .status-message {
      color: #28a745;
      font-weight: bold;
      margin-top: 20px;
    }

    .footer {
      margin-top: 30px;
      color: #666;
      font-size: 0.9em;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(-20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .reboot-container {
      animation: fadeIn 0.5s ease-out;
    }

    @keyframes pulse {
      0%, 100% { 
        transform: scale(1);
        opacity: 1;
      }
      50% { 
        transform: scale(1.05);
        opacity: 0.8;
      }
    }

    .countdown-number {
      animation: pulse 1s ease-in-out infinite;
    }

    @media (max-width: 480px) {
      .reboot-container {
        margin: 20px;
        padding: 30px 20px;
      }
      
      .reboot-icon {
        font-size: 3em;
      }
      
      h1 {
        font-size: 1.8em;
      }
      
      .countdown-number {
        font-size: 2.5em;
      }
      
      .progress-ring {
        width: 100px;
        height: 100px;
      }
    }
  </style>
</head>
<body>
  <div class="reboot-container">
    <div class="reboot-icon">🔄</div>
    <h1>System Rebooting</h1>
    <div class="message">
      The HUB75 Display is restarting. Please wait while the system initializes...
    </div>
    
    <div class="countdown-container">
      <div class="countdown-label">
        Returning to main page in:
      </div>
      
      <svg class="progress-ring">
        <circle class="progress-ring__circle"></circle>
        <circle class="progress-ring__progress" id="progress"></circle>
      </svg>
      
      <div class="countdown-number" id="countdown">5</div>
      <div class="status-message">
        🟢 Reboot sequence initiated
      </div>
    </div>
    
    <div class="footer">
      Please do not close this window during the reboot process
    </div>
  </div>

  <script type="text/javascript">
    let seconds = 5;
    const totalSeconds = 5;
    const progressCircle = document.getElementById('progress');
    const countdownEl = document.getElementById('countdown');
    const circumference = 2 * Math.PI * 52; // 2πr where r=52
    
    // Set initial progress
    progressCircle.style.strokeDasharray = circumference;
    progressCircle.style.strokeDashoffset = circumference;
    
    function updateProgress() {
      const progress = (totalSeconds - seconds) / totalSeconds;
      const offset = circumference - progress * circumference;
      progressCircle.style.strokeDashoffset = offset;
    }
    
    function countdown() {
      seconds = seconds - 1;
      countdownEl.textContent = seconds;
      updateProgress();
      
      if (seconds < 0) {
        window.location = "/";
      } else {
        setTimeout(countdown, 1000);
      }
    }
    
    // Start countdown
    setTimeout(countdown, 1000);
  </script>
</body>
</html>