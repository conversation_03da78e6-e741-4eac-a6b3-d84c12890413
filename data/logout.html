<!DOCTYPE HTML>
<html lang="en">
<head>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta charset="UTF-8">
  <title>Logged Out - HUB75 Display</title>
  <style>
    body {
      background-color: #121212;
      color: white;
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      user-select: none;
    }

    .logout-container {
      background-color: #333;
      border-radius: 15px;
      padding: 40px;
      text-align: center;
      box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      max-width: 400px;
      width: 100%;
    }

    .logout-icon {
      font-size: 4em;
      margin-bottom: 20px;
      color: #FFD65C;
    }

    h1 {
      color: #FFD65C;
      margin-bottom: 20px;
      font-size: 2em;
    }

    .message {
      color: #ccc;
      margin-bottom: 30px;
      font-size: 1.1em;
      line-height: 1.5;
    }

    .btn {
      background-color: #FFD65C;
      color: #003249;
      border: none;
      padding: 15px 30px;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      border-radius: 8px;
      transition: all 0.3s;
      text-decoration: none;
      display: inline-block;
      min-width: 150px;
    }

    .btn:hover {
      background-color: #FFC107;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(255, 213, 92, 0.3);
    }

    .btn:active {
      transform: translateY(0);
    }

    .footer {
      margin-top: 30px;
      color: #666;
      font-size: 0.9em;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(-20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .logout-container {
      animation: fadeIn 0.5s ease-out;
    }

    @media (max-width: 480px) {
      .logout-container {
        margin: 20px;
        padding: 30px 20px;
      }
      
      .logout-icon {
        font-size: 3em;
      }
      
      h1 {
        font-size: 1.5em;
      }
    }
  </style>
</head>
<body>
  <div class="logout-container">
    <div class="logout-icon">🚪</div>
    <h1>Logged Out</h1>
    <div class="message">
      You have been successfully logged out of the HUB75 Pixel Art Display control panel.
    </div>
    <a href="/" class="btn">🔑 Log Back In</a>
    <div class="footer">
      Thank you for using HUB75 Display Controller
    </div>
  </div>
</body>
</html>