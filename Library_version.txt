ESP_Async_WebServer (version 3.7.7) - This should have the HTTP method constants
Async_TCP (version 3.4.4) - This should work with the newer ESPAsyncWebServer

ESPAsyncTCP - This is for ESP8266, not needed for ESP32


Why This Worked
Library Bug Fix: The ESP_Async_WebServer library version 3.7.7 had incomplete HTTP method definitions - it was trying to use HTTP_GET, HTTP_ANY, etc. but didn't properly define them.
Correct Include Order: By defining the HTTP methods before including <ESPAsyncWebServer.h>, both the library code and your code can use the same definitions.
Macro Approach: Using #define instead of enum avoids conflicts with the library's incomplete enum definition





Used library                             Version Path
SPIFFS                                   3.2.0   /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS
FS                                       3.2.0   /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS
AnimatedGIF                              2.2.0   /home/<USER>/Documents/Arduino/libraries/AnimatedGIF
ESP32 HUB75 LED MATRIX PANEL DMA Display 3.0.11  /home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display
Adafruit GFX Library                     1.12.1  /home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library
Adafruit BusIO                           1.17.2  /home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO
Wire                                     3.2.0   /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire
SPI                                      3.2.0   /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI
WiFi                                     3.2.0   /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi
Networking                               3.2.0   /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network
Async TCP                                3.4.4   /home/<USER>/Documents/Arduino/libraries/Async_TCP
GFX_Lite                                 1.0.0   /home/<USER>/Documents/Arduino/libraries/GFX_Lite
ESP Async WebServer                      3.7.7   /home/<USER>/Documents/Arduino/libraries/ESP_Async_WebServer
