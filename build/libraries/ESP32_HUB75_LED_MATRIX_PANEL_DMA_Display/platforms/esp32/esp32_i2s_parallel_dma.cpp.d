/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone/build/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/platforms/esp32/esp32_i2s_parallel_dma.cpp.o: \
 /home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src/platforms/esp32/esp32_i2s_parallel_dma.cpp \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include/sdkconfig.h
