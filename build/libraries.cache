["/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "/home/<USER>/Documents/Arduino/libraries/AnimatedGIF/src", "/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "/home/<USER>/Documents/Arduino/libraries/Async_TCP/src", "/home/<USER>/Documents/Arduino/libraries/GFX_Lite/src", "/home/<USER>/Documents/Arduino/libraries/ESP_Async_WebServer/src", "/home/<USER>/Documents/Arduino/libraries/TJpg_Decoder/src", "/home/<USER>/Documents/Arduino/libraries/LittleFS/src", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SD/src", "/home/<USER>/Documents/Arduino/libraries/PNGdec/src", "/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src"]