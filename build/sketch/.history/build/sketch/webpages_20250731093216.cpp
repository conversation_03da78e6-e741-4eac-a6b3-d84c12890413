#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone/.history/build/sketch/webpages_20250731093216.cpp"
#line 1 "/home/<USER>/Downloads/morphing_clock1/esp32_divoom_clone/webpages.cpp"
#include "webpages.h"
#include "config.h"
#include "display.h"
#include <SPIFFS.h>

// Template processor function - now mainly used for dynamic template replacement
String processor(const String& var) {
  if (var == "FIRMWARE") {
    return FIRMWARE_VERSION;
  }

  if (var == "FREEFLASH") {
    return humanReadableSize((SPIFFS.totalBytes() - SPIFFS.usedBytes()));
  }

  if (var == "USEDFLASH") {
    return humanReadableSize(SPIFFS.usedBytes()));
  }

  if (var == "TOTALFLASH") {
    return humanReadableSize(SPIFFS.totalBytes()));
  }
  
  if (var == "SLIDERVALUE"){
    return sliderValue;
  }
  return String();
}