#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone/.history/build/sketch/webpages_20250731093107.cpp"
#line 1 "/home/<USER>/Downloads/morphing_clock1/esp32_divoom_clone/webpages.cpp"
#include "webpages.h"
#include "config.h"
#include "display.h"
#include <LittleFS.h>

// Template processor function - now mainly used for dynamic template replacement
String processor(const String& var) {
  if (var == "FIRMWARE") {
    return FIRMWARE_VERSION;
  }

  if (var == "FREEFLASH") {
    return humanReadableSize((LittleFS.totalBytes() - LittleFS.usedBytes()));
  }

  if (var == "USEDFLASH") {
    return humanReadableSize(LittleFS.usedBytes()));
  }

  if (var == "TOTALFLASH") {
    return humanReadableSize(LittleFS.totalBytes()));
  }
  
  if (var == "SLIDERVALUE"){
    return sliderValue;
  }
  return String();
}