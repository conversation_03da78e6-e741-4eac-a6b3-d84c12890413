#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone/.history/config_20250730172738.cpp"
#include "config.h"
#include <ESP32.h>

// Default configuration values
const String default_ssid = "SSID";
const String default_wifipassword = "Password";
const String default_httpuser = "admin";
const String default_httppassword = "admin";
const int default_webserverporthttp = 80;
const char* ntpServer = "pool.ntp.org";
const char* PARAM_INPUT = "value";
const long gmtOffset_sec = 0;
const int daylightOffset_sec = 0;
const int maxGIFsPerPage = 4;

// Global configuration instance
Config config;
bool shouldReboot = false;

void initializeConfig() {
  config.ssid = default_ssid;
  config.wifipassword = default_wifipassword;
  config.httpuser = default_httpuser;
  config.httppassword = default_httppassword;
  config.webserverporthttp = default_webserverporthttp;
}

void rebootESP(String message) {
  Serial.print("Rebooting ESP32: "); 
  Serial.println(message);
  ESP.restart();
}