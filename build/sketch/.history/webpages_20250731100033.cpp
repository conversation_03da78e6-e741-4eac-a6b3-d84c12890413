#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone/.history/webpages_20250731100033.cpp"
#include "webpages.h"
#include "config.h"
#include "display.h"
#include <SPIFFS.h>

// External declarations for constants and variables defined in main .ino file
extern const char* FIRMWARE_VERSION;
extern String sliderValue;

// External function declarations
extern String humanReadableSize(const size_t bytes);

// Template processor function - now mainly used for dynamic template replacement
String processor(const String& var) {
  if (var == "FIRMWARE") {
    return FIRMWARE_VERSION;
  }

  if (var == "FREEFLASH") {
    return humanReadableSize((SPIFFS.totalBytes() - SPIFFS.usedBytes()));
  }

  if (var == "USEDFLASH") {
    return humanReadableSize(SPIFFS.usedBytes());
  }

  if (var == "TOTALFLASH") {
    return humanReadableSize(SPIFFS.totalBytes());
  }

  if (var == "SLIDERVALUE"){
    return sliderValue;
  }
  return String();
}