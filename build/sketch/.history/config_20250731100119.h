#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone/.history/config_20250731100119.h"
#ifndef CONFIG_H
#define CONFIG_H

#include <Arduino.h>

// Firmware and filesystem constants
#define FIRMWARE_VERSION "v0.4.5b"
#define FILESYSTEM SPIFFS

// Hardware pin definitions
#define A_PIN   22 
#define B_PIN   32 
#define C_PIN   33
#define D_PIN   17 
#define E_PIN   21 

// Panel configuration
#define PANEL_RES_X 64      
#define PANEL_RES_Y 64     
#define PANEL_CHAIN 1      

// Default configuration values
extern const String default_ssid;
extern const String default_wifipassword;
extern const String default_httpuser;
extern const String default_httppassword;
extern const int default_webserverporthttp;
extern const char* ntpServer;
extern const char* PARAM_INPUT;
extern const long gmtOffset_sec;
extern const int daylightOffset_sec;
extern const int maxGIFsPerPage;

// Configuration structure
struct Config {
  String ssid;
  String wifipassword;
  String httpuser;
  String httppassword;
  int webserverporthttp;
};

// Global configuration variables
extern Config config;
extern bool shouldReboot;

// Configuration functions
void initializeConfig();
void rebootESP(String message);

#endif