#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone/.history/webserver_20250731102251.h"
#ifndef WEBSERVER_H
#define WEBSERVER_H

#include "config.h"
#include <ESPAsyncWebServer.h>
#include <WiFi.h>

// Web server globals
extern AsyncWebServer *server;

// Web server functions
void setupWiFi();
void setupWebServer();
void configureWebServer();
void notFound(AsyncWebServerRequest *request);
bool checkUserWebAuth(AsyncWebServerRequest * request);
void handleUpload(AsyncWebServerRequest *request, String filename, size_t index, uint8_t *data, size_t len, bool final);
String listFiles(bool ishtml = false, int page = 1, int pageSize = 4);

#endif