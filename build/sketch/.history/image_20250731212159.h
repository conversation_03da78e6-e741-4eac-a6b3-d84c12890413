#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone/.history/image_20250731212159.h"
#ifndef IMAGE_H
#define IMAGE_H

#include <SPIFFS.h>
#include <TJpg_Decoder.h>
#include <PNGdec.h>
#include "display.h"

// Image related globals
extern String currentImagePath;
extern String requestedImagePath;
extern char imageFilePath[256];
extern File imageFile;
extern unsigned long imageDisplayStart;

// Image display settings
extern bool imageEnabled;
extern unsigned long imageDisplayDuration; // How long to display each image (ms)

// Image processing functions
bool isImageFile(const String& fileName);
bool isJpegFile(const String& fileName);
bool isPngFile(const String& fileName);
bool isBmpFile(const String& fileName);

// JPEG functions
bool jpegRender(int16_t x, int16_t y, uint16_t w, uint16_t h, uint16_t* bitmap);
void ShowJPEG(const char* filename);

// PNG functions  
void pngDraw(PNGDRAW *pDraw);
void ShowPNG(const char* filename);

// BMP functions
void ShowBMP(const char* filename);

// Generic image display function
void ShowImage(const char* filename);

// Image scaling functions
void scaleImage(uint16_t* srcBuffer, int srcWidth, int srcHeight, 
                uint16_t* dstBuffer, int dstWidth, int dstHeight);

// Utility functions
uint16_t rgb888to565(uint8_t r, uint8_t g, uint8_t b);
void clearImageDisplay();

#endif
