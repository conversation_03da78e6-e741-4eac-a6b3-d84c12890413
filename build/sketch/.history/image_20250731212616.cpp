#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone/.history/image_20250731212616.cpp"
#include "image.h"
#include <SPIFFS.h>
#include <TJpg_Decoder.h>
#include <PNGdec.h>

// Image globals
String currentImagePath = "";
String requestedImagePath = "";
char imageFilePath[256] = { 0 };
File imageFile;
unsigned long imageDisplayStart = 0;

// Image display settings
bool imageEnabled = true;
unsigned long imageDisplayDuration = 5000; // 5 seconds per image

// Image buffer for scaling operations
uint16_t* imageBuffer = nullptr;
const int MAX_IMAGE_SIZE = 128 * 128; // Maximum supported image size for processing

// Helper function to check if a file is an image
bool isImageFile(const String& fileName) {
  return isJpegFile(fileName) || isPngFile(fileName) || isBmpFile(fileName);
}

bool isJpegFile(const String& fileName) {
  return fileName.endsWith(".jpg") || fileName.endsWith(".JPG") || 
         fileName.endsWith(".jpeg") || fileName.endsWith(".JPEG");
}

bool isPngFile(const String& fileName) {
  return fileName.endsWith(".png") || fileName.endsWith(".PNG");
}

bool isBmpFile(const String& fileName) {
  return fileName.endsWith(".bmp") || fileName.endsWith(".BMP");
}

// Convert RGB888 to RGB565
uint16_t rgb888to565(uint8_t r, uint8_t g, uint8_t b) {
  return ((r & 0xF8) << 8) | ((g & 0xFC) << 3) | (b >> 3);
}

// JPEG render callback function
bool jpegRender(int16_t x, int16_t y, uint16_t w, uint16_t h, uint16_t* bitmap) {
  // Calculate centering offset
  int16_t x_offset = (MATRIX_WIDTH - w) / 2;
  int16_t y_offset = (MATRIX_HEIGHT - h) / 2;
  
  // Draw the bitmap to the display
  for (int16_t j = 0; j < h; j++) {
    for (int16_t i = 0; i < w; i++) {
      int16_t px = x_offset + x + i;
      int16_t py = y_offset + y + j;
      
      if (px >= 0 && px < MATRIX_WIDTH && py >= 0 && py < MATRIX_HEIGHT) {
        gfx_layer_bg.drawPixel(px, py, bitmap[j * w + i]);
      }
    }
  }
  return true;
}

// Show JPEG image
void ShowJPEG(const char* filename) {
  Serial.print("Displaying JPEG: ");
  Serial.println(filename);
  
  // Clear the background layer
  gfx_layer_bg.clear();
  
  // Set the callback function
  TJpgDec.setCallback(jpegRender);
  
  // Decode and display the JPEG
  if (TJpgDec.drawFsJpg(0, 0, filename, SPIFFS) == JDR_OK) {
    Serial.println("JPEG displayed successfully");
  } else {
    Serial.println("ERROR: Failed to display JPEG");
  }
}

// PNG draw callback function
void pngDraw(PNGDRAW *pDraw) {
  uint16_t lineBuffer[MATRIX_WIDTH];
  png_color *palette = nullptr;
  uint8_t *s = pDraw->pPixels;
  
  // Calculate centering offset
  int16_t x_offset = (MATRIX_WIDTH - pDraw->iWidth) / 2;
  int16_t y_offset = (MATRIX_HEIGHT - pDraw->iHeight) / 2;
  int16_t y = y_offset + pDraw->y;
  
  if (y < 0 || y >= MATRIX_HEIGHT) return;
  
  // Convert pixels based on PNG type
  for (int x = 0; x < pDraw->iWidth && x < MATRIX_WIDTH; x++) {
    uint16_t pixel;
    int16_t px = x_offset + x;
    
    if (px < 0 || px >= MATRIX_WIDTH) continue;
    
    if (pDraw->iBpp == 16) {
      // 16-bit RGB565
      pixel = ((uint16_t*)s)[x];
    } else if (pDraw->iBpp == 8) {
      // 8-bit palette
      if (pDraw->pPalette) {
        palette = (png_color*)pDraw->pPalette;
        uint8_t idx = s[x];
        pixel = rgb888to565(palette[idx].red, palette[idx].green, palette[idx].blue);
      } else {
        // Grayscale
        pixel = rgb888to565(s[x], s[x], s[x]);
      }
    } else {
      // 24-bit RGB
      pixel = rgb888to565(s[x*3], s[x*3+1], s[x*3+2]);
    }
    
    gfx_layer_bg.drawPixel(px, y, pixel);
  }
}

// Show PNG image
void ShowPNG(const char* filename) {
  Serial.print("Displaying PNG: ");
  Serial.println(filename);
  
  // Clear the background layer
  gfx_layer_bg.clear();
  
  PNG png;
  File pngFile = SPIFFS.open(filename, "r");
  
  if (!pngFile) {
    Serial.println("ERROR: Could not open PNG file");
    return;
  }
  
  int rc = png.open(pngFile, pngDraw);
  if (rc == PNG_SUCCESS) {
    Serial.printf("PNG Info: %d x %d, %d bpp\n", png.getWidth(), png.getHeight(), png.getBpp());
    png.decode(NULL, 0);
    Serial.println("PNG displayed successfully");
  } else {
    Serial.printf("ERROR: Failed to open PNG: %d\n", rc);
  }
  
  pngFile.close();
}

// Simple BMP reader for 24-bit uncompressed BMP files
void ShowBMP(const char* filename) {
  Serial.print("Displaying BMP: ");
  Serial.println(filename);

  File bmpFile = SPIFFS.open(filename, "r");
  if (!bmpFile) {
    Serial.println("ERROR: Could not open BMP file");
    return;
  }

  // Read BMP header
  uint8_t header[54];
  if (bmpFile.read(header, 54) != 54) {
    Serial.println("ERROR: Invalid BMP header");
    bmpFile.close();
    return;
  }

  // Check BMP signature
  if (header[0] != 'B' || header[1] != 'M') {
    Serial.println("ERROR: Not a valid BMP file");
    bmpFile.close();
    return;
  }

  // Extract image info
  uint32_t dataOffset = *(uint32_t*)&header[10];
  uint32_t width = *(uint32_t*)&header[18];
  uint32_t height = *(uint32_t*)&header[22];
  uint16_t bitsPerPixel = *(uint16_t*)&header[28];

  Serial.printf("BMP Info: %dx%d, %d bpp\n", width, height, bitsPerPixel);

  if (bitsPerPixel != 24) {
    Serial.println("ERROR: Only 24-bit BMP files are supported");
    bmpFile.close();
    return;
  }

  // Clear the background layer
  gfx_layer_bg.clear();

  // Calculate centering offset
  int16_t x_offset = (MATRIX_WIDTH - width) / 2;
  int16_t y_offset = (MATRIX_HEIGHT - height) / 2;

  // Seek to pixel data
  bmpFile.seek(dataOffset);

  // BMP rows are stored bottom-to-top
  uint32_t rowSize = ((width * 3 + 3) & ~3); // Row size padded to 4 bytes
  uint8_t* rowBuffer = (uint8_t*)malloc(rowSize);

  if (!rowBuffer) {
    Serial.println("ERROR: Could not allocate memory for BMP processing");
    bmpFile.close();
    return;
  }

  for (int32_t y = height - 1; y >= 0; y--) {
    bmpFile.seek(dataOffset + y * rowSize);
    bmpFile.read(rowBuffer, rowSize);

    for (uint32_t x = 0; x < width; x++) {
      if (x >= MATRIX_WIDTH || y >= MATRIX_HEIGHT) continue;

      int16_t px = x_offset + x;
      int16_t py = y_offset + (height - 1 - y);

      if (px >= 0 && px < MATRIX_WIDTH && py >= 0 && py < MATRIX_HEIGHT) {
        // BMP uses BGR format
        uint8_t b = rowBuffer[x * 3];
        uint8_t g = rowBuffer[x * 3 + 1];
        uint8_t r = rowBuffer[x * 3 + 2];

        uint16_t pixel = rgb888to565(r, g, b);
        gfx_layer_bg.drawPixel(px, py, pixel);
      }
    }
  }

  free(rowBuffer);
  bmpFile.close();
  Serial.println("BMP displayed successfully");
}

// Generic image display function
void ShowImage(const char* filename) {
  String fileName = String(filename);

  if (isJpegFile(fileName)) {
    ShowJPEG(filename);
  } else if (isPngFile(fileName)) {
    ShowPNG(filename);
  } else if (isBmpFile(fileName)) {
    ShowBMP(filename);
  } else {
    Serial.println("ERROR: Unsupported image format");
    return;
  }

  // Update display layers
  gfx_layer_bg.dim(150);
  gfx_layer_fg.dim(255);
  gfx_compositor.Blend(gfx_layer_bg, gfx_layer_fg);

  imageDisplayStart = millis();
}

// Clear image display
void clearImageDisplay() {
  gfx_layer_bg.clear();
  gfx_layer_fg.clear();
}
