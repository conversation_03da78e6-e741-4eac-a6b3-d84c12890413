#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone/webserver.h"
#ifndef WEBSERVER_H
#define WEBSERVER_H

#include "config.h"

// Fix for broken ESP_Async_WebServer library - define missing HTTP methods
#define HTTP_GET     1
#define HTTP_POST    2
#define HTTP_DELETE  4
#define HTTP_PUT     8
#define HTTP_PATCH   16
#define HTTP_HEAD    32
#define HTTP_OPTIONS 64
#define HTTP_ANY     127

#include <ESPAsyncWebServer.h>
#include <WiFi.h>

// Web server globals
extern AsyncWebServer *server;

// Web server functions
void setupWiFi();
void setupWebServer();
void configureWebServer();
void notFound(AsyncWebServerRequest *request);
bool checkUserWebAuth(AsyncWebServerRequest * request);
void handleUpload(AsyncWebServerRequest *request, String filename, size_t index, uint8_t *data, size_t len, bool final);
String listFiles(bool ishtml = false, int page = 1, int pageSize = 4);
String listFilesJSON(int page = 1, int itemsPerPage = 10);

#endif