#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone/LIBRARY_DEPENDENCIES.md"
# Library Dependencies for Image Support

## Required Libraries

To use the new image support functionality, you need to install the following libraries in your Arduino IDE:

### 1. TJpg_Decoder
- **Purpose**: JPEG image decoding
- **Author**: <PERSON><PERSON><PERSON>
- **Installation**: Arduino Library Manager -> Search "TJpg_Decoder"
- **GitHub**: https://github.com/Bodmer/TJpg_Decoder

### 2. PNGdec
- **Purpose**: PNG image decoding
- **Author**: <PERSON>
- **Installation**: Arduino Library Manager -> Search "PNGdec"
- **GitHub**: https://github.com/bitbank2/PNGdec

### 3. ArduinoJson
- **Purpose**: JSON parsing for clockface definitions
- **Author**: <PERSON><PERSON>
- **Installation**: Arduino Library Manager -> Search "ArduinoJson"
- **GitHub**: https://github.com/bblanchon/ArduinoJson
- **Required Version**: 6.19.0 or later

## Installation Instructions

### Method 1: Arduino IDE Library Manager
1. Open Arduino IDE
2. Go to Tools -> Manage Libraries
3. Search for "TJpg_Decoder" and install the latest version
4. Search for "PNGdec" and install the latest version
5. Search for "ArduinoJson" and install version 6.19.0 or later

### Method 2: Manual Installation
1. Download the libraries from their GitHub repositories
2. Extract to your Arduino libraries folder
3. Restart Arduino IDE

## Existing Dependencies
The following libraries were already required and remain unchanged:
- ESP32-HUB75-MatrixPanel-I2S-DMA
- AnimatedGIF
- ESPAsyncWebServer
- AsyncTCP
- SPIFFS (built-in)
- WiFi (built-in)
- mbedtls (built-in ESP32 library for base64 decoding)

## Compilation Notes
- Make sure you have ESP32 board package version 2.0.0 or later
- The libraries are compatible with ESP32-S3 and other ESP32 variants
- Total additional flash usage: approximately 50KB for both libraries

## Version Compatibility
- TJpg_Decoder: Version 1.0.8 or later recommended
- PNGdec: Version 1.0.1 or later recommended
- ArduinoJson: Version 6.19.0 or later required (for clockface support)
- ESP32 Arduino Core: Version 2.0.0 or later required

## Troubleshooting Library Issues

### Compilation Errors
If you get compilation errors:
1. Verify all libraries are installed correctly
2. Check that library versions are compatible
3. Clean and rebuild the project
4. Restart Arduino IDE

### Memory Issues
If you encounter memory issues:
1. The libraries use minimal RAM due to streaming decoding
2. PSRAM is not required but can help with larger images
3. Monitor free heap memory during operation

### Performance Issues
For optimal performance:
1. Use JPEG for photographs (smaller file size, faster decoding)
2. Use PNG for graphics with transparency
3. Keep image files under 100KB when possible
4. Consider image dimensions (64x64 is optimal)
