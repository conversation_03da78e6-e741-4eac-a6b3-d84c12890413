/home/<USER>/Downloads/morphing_clock1_Ubuntu/clockface/esp32_divoom_clone/build/core/esp32-hal-ledc.c.o: \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-ledc.c \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/include/soc/soc_caps.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/include/soc/mpu_caps.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_system/include/esp_system.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_common/include/esp_err.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_common/include/esp_compiler.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_common/include/esp_attr.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_common/include/esp_bit_defs.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_common/include/esp_idf_version.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_hw_support/include/esp_sleep.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/include/hal/touch_sensor_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/include/hal/gpio_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/include/soc/gpio_num.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/FreeRTOS.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/config/include/freertos/FreeRTOSConfig.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/config/xtensa/include/freertos/FreeRTOSConfig_arch.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/include/xtensa_config.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/include/xtensa/hal.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/esp32s3/include/xtensa/config/core.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/include/xtensa/xtensa-versions.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/esp32s3/include/xtensa/config/core-isa.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/esp32s3/include/xtensa/config/core-matmap.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/esp32s3/include/xtensa/config/tie.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/esp32s3/include/xtensa/config/system.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/include/xtensa_context.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/include/xtensa/corebits.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/include/xtensa/xtruntime-frames.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/projdefs.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/portable.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/deprecated_definitions.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos/portmacro.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/include/xtensa/xtruntime.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/esp32s3/include/xtensa/config/specreg.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/include/xtensa/xtruntime-core-state.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/include/xt_instr_macros.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/include/xtensa/xtruntime.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_hw_support/include/spinlock.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_hw_support/include/esp_cpu.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/include/xtensa_api.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/include/xt_utils.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/xtensa/esp32s3/include/xtensa/config/extreg.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_hw_support/include/esp_intr_alloc.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_hw_support/include/esp_intr_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_system/include/esp_private/crosscore_int.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_common/include/esp_macros.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_common/include/esp_assert.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_hw_support/include/esp_memory_utils.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/include/soc/soc.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_common/include/esp_assert.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/register/soc/reg_base.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/newlib/platform_include/esp_newlib.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/heap/include/esp_heap_caps.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/heap/include/multi_heap.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_rom/include/esp_rom_sys.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/include/soc/reset_reasons.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos/portbenchmark.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_timer/include/esp_timer.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_hw_support/include/esp_etm.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/mpu_wrappers.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/esp_additions/include/freertos/idf_additions.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/task.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/list.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/queue.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/task.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/semphr.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/queue.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/stream_buffer.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/message_buffer.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/stream_buffer.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/event_groups.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/freertos/FreeRTOS-Kernel/include/freertos/timers.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-log.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_rom/esp32s3/include/esp32s3/rom/ets_sys.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/log/include/esp_log.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/log/include/esp_log_level.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/log/include/esp_log_color.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/log/include/esp_log_buffer.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/log/include/esp_log_timestamp.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-matrix.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/include/soc/gpio_sig_map.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-uart.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/include/soc/uart_pins.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/register/soc/io_mux_reg.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/include/hal/uart_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/include/soc/clk_tree_defs.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-gpio.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3/pins_arduino.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_driver_gpio/include/driver/gpio.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_rom/include/esp_rom_gpio.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/include/soc/gpio_pins.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_driver_gpio/include/driver/gpio_etm.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-touch.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-touch-ng.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-dac.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-adc.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-spi.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-i2c.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-ledc.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/include/hal/ledc_types.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-rmt.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-sigmadelta.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-timer.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_driver_gptimer/include/driver/gptimer_types.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/include/hal/timer_types.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-bt.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-psram.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-rgb-led.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-cpu.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/esp_driver_ledc/include/driver/ledc.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-periman.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/esp32s3/include/hal/ledc_ll.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/include/soc/ledc_periph.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/register/soc/ledc_reg.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/register/soc/ledc_struct.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/soc/esp32s3/register/soc/system_struct.h \
 /home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/hal/platform_port/include/hal/assert.h
