#pragma once 

#include <Arduino.h>
#include <gfxfont.h>

// Picopixel by <PERSON>.  A tiny font
// with all characters within a 6 pixel height.

const uint8_t PicopixelBitmaps[] PROGMEM = {
    0xE8, 0xB4, 0x57, 0xD5, 0xF5, 0x00, 0x4E, 0x3E, 0x80, 0xA5, 0x4A, 0x4A,
    0x5A, 0x50, 0xC0, 0x6A, 0x40, 0x95, 0x80, 0xAA, 0x80, 0x5D, 0x00, 0x60,
    0xE0, 0x80, 0x25, 0x48, 0x56, 0xD4, 0x75, 0x40, 0xC5, 0x4E, 0xC5, 0x1C,
    0x97, 0x92, 0xF3, 0x1C, 0x53, 0x54, 0xE5, 0x48, 0x55, 0x54, 0x55, 0x94,
    0xA0, 0x46, 0x64, 0xE3, 0x80, 0x98, 0xC5, 0x04, 0x56, 0xC6, 0x57, 0xDA,
    0xD7, 0x5C, 0x72, 0x46, 0xD6, 0xDC, 0xF3, 0xCE, 0xF3, 0x48, 0x72, 0xD4,
    0xB7, 0xDA, 0xF8, 0x24, 0xD4, 0xBB, 0x5A, 0x92, 0x4E, 0x8E, 0xEB, 0x58,
    0x80, 0x9D, 0xB9, 0x90, 0x56, 0xD4, 0xD7, 0x48, 0x56, 0xD4, 0x40, 0xD7,
    0x5A, 0x71, 0x1C, 0xE9, 0x24, 0xB6, 0xD4, 0xB6, 0xA4, 0x8C, 0x6B, 0x55,
    0x00, 0xB5, 0x5A, 0xB5, 0x24, 0xE5, 0x4E, 0xEA, 0xC0, 0x91, 0x12, 0xD5,
    0xC0, 0x54, 0xF0, 0x90, 0xC7, 0xF0, 0x93, 0x5E, 0x71, 0x80, 0x25, 0xDE,
    0x5E, 0x30, 0x6E, 0x80, 0x77, 0x9C, 0x93, 0x5A, 0xB8, 0x45, 0x60, 0x92,
    0xEA, 0xAA, 0x40, 0xD5, 0x6A, 0xD6, 0x80, 0x55, 0x00, 0xD7, 0x40, 0x75,
    0x90, 0xE8, 0x71, 0xE0, 0xBA, 0x40, 0xB5, 0x80, 0xB5, 0x00, 0x8D, 0x54,
    0xAA, 0x80, 0xAC, 0xE0, 0xE5, 0x70, 0x6A, 0x26, 0xFC, 0xC8, 0xAC, 0x5A};

const GFXglyph PicopixelGlyphs[] PROGMEM = {{0, 0, 0, 2, 0, 1},     // 0x20 ' '
                                            {0, 1, 5, 2, 0, -4},    // 0x21 '!'
                                            {1, 3, 2, 4, 0, -4},    // 0x22 '"'
                                            {2, 5, 5, 6, 0, -4},    // 0x23 '#'
                                            {6, 3, 6, 4, 0, -4},    // 0x24 '$'
                                            {9, 3, 5, 4, 0, -4},    // 0x25 '%'
                                            {11, 4, 5, 5, 0, -4},   // 0x26 '&'
                                            {14, 1, 2, 2, 0, -4},   // 0x27 '''
                                            {15, 2, 5, 3, 0, -4},   // 0x28 '('
                                            {17, 2, 5, 3, 0, -4},   // 0x29 ')'
                                            {19, 3, 3, 4, 0, -3},   // 0x2A '*'
                                            {21, 3, 3, 4, 0, -3},   // 0x2B '+'
                                            {23, 2, 2, 3, 0, 0},    // 0x2C ','
                                            {24, 3, 1, 4, 0, -2},   // 0x2D '-'
                                            {25, 1, 1, 2, 0, 0},    // 0x2E '.'
                                            {26, 3, 5, 4, 0, -4},   // 0x2F '/'
                                            {28, 3, 5, 4, 0, -4},   // 0x30 '0'
                                            {30, 2, 5, 3, 0, -4},   // 0x31 '1'
                                            {32, 3, 5, 4, 0, -4},   // 0x32 '2'
                                            {34, 3, 5, 4, 0, -4},   // 0x33 '3'
                                            {36, 3, 5, 4, 0, -4},   // 0x34 '4'
                                            {38, 3, 5, 4, 0, -4},   // 0x35 '5'
                                            {40, 3, 5, 4, 0, -4},   // 0x36 '6'
                                            {42, 3, 5, 4, 0, -4},   // 0x37 '7'
                                            {44, 3, 5, 4, 0, -4},   // 0x38 '8'
                                            {46, 3, 5, 4, 0, -4},   // 0x39 '9'
                                            {48, 1, 3, 2, 0, -3},   // 0x3A ':'
                                            {49, 2, 4, 3, 0, -3},   // 0x3B ';'
                                            {50, 2, 3, 3, 0, -3},   // 0x3C '<'
                                            {51, 3, 3, 4, 0, -3},   // 0x3D '='
                                            {53, 2, 3, 3, 0, -3},   // 0x3E '>'
                                            {54, 3, 5, 4, 0, -4},   // 0x3F '?'
                                            {56, 3, 5, 4, 0, -4},   // 0x40 '@'
                                            {58, 3, 5, 4, 0, -4},   // 0x41 'A'
                                            {60, 3, 5, 4, 0, -4},   // 0x42 'B'
                                            {62, 3, 5, 4, 0, -4},   // 0x43 'C'
                                            {64, 3, 5, 4, 0, -4},   // 0x44 'D'
                                            {66, 3, 5, 4, 0, -4},   // 0x45 'E'
                                            {68, 3, 5, 4, 0, -4},   // 0x46 'F'
                                            {70, 3, 5, 4, 0, -4},   // 0x47 'G'
                                            {72, 3, 5, 4, 0, -4},   // 0x48 'H'
                                            {74, 1, 5, 2, 0, -4},   // 0x49 'I'
                                            {75, 3, 5, 4, 0, -4},   // 0x4A 'J'
                                            {77, 3, 5, 4, 0, -4},   // 0x4B 'K'
                                            {79, 3, 5, 4, 0, -4},   // 0x4C 'L'
                                            {81, 5, 5, 6, 0, -4},   // 0x4D 'M'
                                            {85, 4, 5, 5, 0, -4},   // 0x4E 'N'
                                            {88, 3, 5, 4, 0, -4},   // 0x4F 'O'
                                            {90, 3, 5, 4, 0, -4},   // 0x50 'P'
                                            {92, 3, 6, 4, 0, -4},   // 0x51 'Q'
                                            {95, 3, 5, 4, 0, -4},   // 0x52 'R'
                                            {97, 3, 5, 4, 0, -4},   // 0x53 'S'
                                            {99, 3, 5, 4, 0, -4},   // 0x54 'T'
                                            {101, 3, 5, 4, 0, -4},  // 0x55 'U'
                                            {103, 3, 5, 4, 0, -4},  // 0x56 'V'
                                            {105, 5, 5, 6, 0, -4},  // 0x57 'W'
                                            {109, 3, 5, 4, 0, -4},  // 0x58 'X'
                                            {111, 3, 5, 4, 0, -4},  // 0x59 'Y'
                                            {113, 3, 5, 4, 0, -4},  // 0x5A 'Z'
                                            {115, 2, 5, 3, 0, -4},  // 0x5B '['
                                            {117, 3, 5, 4, 0, -4},  // 0x5C '\'
                                            {119, 2, 5, 3, 0, -4},  // 0x5D ']'
                                            {121, 3, 2, 4, 0, -4},  // 0x5E '^'
                                            {122, 4, 1, 4, 0, 1},   // 0x5F '_'
                                            {123, 2, 2, 3, 0, -4},  // 0x60 '`'
                                            {124, 3, 4, 4, 0, -3},  // 0x61 'a'
                                            {126, 3, 5, 4, 0, -4},  // 0x62 'b'
                                            {128, 3, 3, 4, 0, -2},  // 0x63 'c'
                                            {130, 3, 5, 4, 0, -4},  // 0x64 'd'
                                            {132, 3, 4, 4, 0, -3},  // 0x65 'e'
                                            {134, 2, 5, 3, 0, -4},  // 0x66 'f'
                                            {136, 3, 5, 4, 0, -3},  // 0x67 'g'
                                            {138, 3, 5, 4, 0, -4},  // 0x68 'h'
                                            {140, 1, 5, 2, 0, -4},  // 0x69 'i'
                                            {141, 2, 6, 3, 0, -4},  // 0x6A 'j'
                                            {143, 3, 5, 4, 0, -4},  // 0x6B 'k'
                                            {145, 2, 5, 3, 0, -4},  // 0x6C 'l'
                                            {147, 5, 3, 6, 0, -2},  // 0x6D 'm'
                                            {149, 3, 3, 4, 0, -2},  // 0x6E 'n'
                                            {151, 3, 3, 4, 0, -2},  // 0x6F 'o'
                                            {153, 3, 4, 4, 0, -2},  // 0x70 'p'
                                            {155, 3, 4, 4, 0, -2},  // 0x71 'q'
                                            {157, 2, 3, 3, 0, -2},  // 0x72 'r'
                                            {158, 3, 4, 4, 0, -3},  // 0x73 's'
                                            {160, 2, 5, 3, 0, -4},  // 0x74 't'
                                            {162, 3, 3, 4, 0, -2},  // 0x75 'u'
                                            {164, 3, 3, 4, 0, -2},  // 0x76 'v'
                                            {166, 5, 3, 6, 0, -2},  // 0x77 'w'
                                            {168, 3, 3, 4, 0, -2},  // 0x78 'x'
                                            {170, 3, 4, 4, 0, -2},  // 0x79 'y'
                                            {172, 3, 4, 4, 0, -3},  // 0x7A 'z'
                                            {174, 3, 5, 4, 0, -4},  // 0x7B '{'
                                            {176, 1, 6, 2, 0, -4},  // 0x7C '|'
                                            {177, 3, 5, 4, 0, -4},  // 0x7D '}'
                                            {179, 4, 2, 5, 0, -3}}; // 0x7E '~'

const GFXfont Picopixel PROGMEM = {(uint8_t *)PicopixelBitmaps,
                                   (GFXglyph *)PicopixelGlyphs, 0x20, 0x7E, 7};
