#ifndef DISPLAY_H
#define DISPLAY_H

#include <ESP32-HUB75-MatrixPanel-I2S-DMA.h>
#include <GFX_Layer.hpp>
#include "config.h"

// Display and graphics globals
extern MatrixPanel_I2S_DMA *dma_display;
extern GFX_Layer gfx_layer_bg;
extern GFX_Layer gfx_layer_fg;
extern GFX_LayerCompositor gfx_compositor;

// Color variables
extern uint16_t myBLACK, myWHITE, myRED, myGREEN, myBLUE;
extern uint8_t colorR, colorG, colorB;
extern uint8_t scrollFontSize;
extern uint8_t scrollSpeed;
extern int16_t xOne, yOne;
extern uint16_t w, h;

// Text and animation variables
extern int textXPosition;
extern int textYPosition;
extern unsigned long lastPixelToggle;
extern unsigned long lastScrollUpdate;
extern unsigned long isAnimationDue;
extern bool showFirstSet;
extern bool clockEnabled;
extern bool gifEnabled;
extern bool scrollTextEnabled;
extern bool loopGifEnabled;
extern bool clockfaceEnabled;

// Text and slider variables
extern String inputMessage;
extern String sliderValue;
extern String scrollText;

// Display functions
void setupDisplay();
void showStartupInfo();
void layer_draw_callback(int16_t x, int16_t y, uint8_t r_data, uint8_t g_data, uint8_t b_data);
String humanReadableSize(const size_t bytes);

#endif