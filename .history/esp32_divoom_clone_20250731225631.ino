//By mzashh https://github.com/mzashh
//Organized version with separate header files

#include "config.h"
#include <SPIFFS.h>
#include <AnimatedGIF.h>
#include <ESP32-HUB75-MatrixPanel-I2S-DMA.h>
#include <WiFi.h>
#include <AsyncTCP.h>
#include <GFX_Layer.hpp>
#include <ESPAsyncWebServer.h>
#include <TJpg_Decoder.h>
#include <PNGdec.h>
#include "time.h"
#include "display.h"
#include "gif.h"
#include "image.h"
#include "webserver.h"

void setup() {
  Serial.begin(115200);
  Serial.print("Firmware: "); Serial.println(FIRMWARE_VERSION);
  Serial.println("Booting ...");

  // Initialize filesystem
  Serial.println("Mounting SPIFFS ...");
  if (!SPIFFS.begin(true)) {
    Serial.println("ERROR: Cannot mount SPIFFS, Rebooting");
    rebootESP("ERROR: Cannot mount SPIFFS, Rebooting");
  }

  // Initialize display
  setupDisplay();

  // Print filesystem info
  Serial.print("Flash Free: "); Serial.println(humanReadableSize((SPIFFS.totalBytes() - SPIFFS.usedBytes())));
  Serial.print("Flash Used: "); Serial.println(humanReadableSize(SPIFFS.usedBytes()));
  Serial.print("Flash Total: "); Serial.println(humanReadableSize(SPIFFS.totalBytes()));

  // Initialize config
  initializeConfig();

  // Setup WiFi
  setupWiFi();

  // Setup web server
  setupWebServer();

  // Setup time
  configTime(gmtOffset_sec, daylightOffset_sec, ntpServer);

  // Show startup info
  showStartupInfo();

  // Initialize GIF playback
  gif.begin(LITTLE_ENDIAN_PIXELS);

  // Initialize image processing libraries with proper settings for RGB565
  TJpgDec.setJpgScale(1);  // No scaling by default, we'll handle it manually
  TJpgDec.setSwapBytes(false);  // Try without byte swapping first
  TJpgDec.setCallback(jpegRender);  // Set the callback function
}

void loop() {
  if (shouldReboot) {
    rebootESP("Web Admin Initiated Reboot");
  }

  while (1) { // Run forever
    root = FILESYSTEM.open(gifDir); // Open the root directory
    if (root) {
      // Check if a new GIF or Image is requested via the play button
      if (!requestedGifPath.isEmpty()) {
        // Play the requested file and set it as the current file
        currentGifPath = requestedGifPath; // Update the current file path
        requestedGifPath = ""; // Clear the requested path

        // Find and play the requested file
        while (gifFile = root.openNextFile()) {
          if (String(gifFile.path()) == currentGifPath) {
            break;
          }
        }

        if (gifFile) {
          String fileName = String(gifFile.path());
          // Check if it's a GIF file
          if (isGifFile(fileName)) {
            // Play the requested GIF
            memset(filePath, 0x0, sizeof(filePath));
            strcpy(filePath, gifFile.path());
            ShowGIF(filePath);

            // If looping is enabled, continue looping the requested GIF
            if (loopGifEnabled) {
              continue; // Restart the loop for the same GIF
            }
          }
          // Check if it's an image file
          else if (isImageFile(fileName)) {
            // Display the requested image
            memset(imageFilePath, 0x0, sizeof(imageFilePath));
            strcpy(imageFilePath, gifFile.path());
            currentImagePath = String(imageFilePath);
            ShowImage(imageFilePath);

            // Wait for the image display duration
            while (millis() - imageDisplayStart < imageDisplayDuration) {
              delay(100);
            }
          }
        }
      } else if (!currentGifPath.isEmpty()) {
        // Resume from the last file (GIF or Image)
        while (gifFile = root.openNextFile()) {
          if (String(gifFile.path()) == currentGifPath) {
            break;
          }
        }
      } else {
        // Open the first media file (GIF or Image) in the directory
        do {
          gifFile = root.openNextFile();
        } while (gifFile && !isGifFile(String(gifFile.path())) && !isImageFile(String(gifFile.path())));
      }

      while (gifFile) {
        if (!gifFile.isDirectory()) { // Play the file if it's not a directory
          String fileName = String(gifFile.path());

          // Handle GIF files
          if (isGifFile(fileName)) {
            memset(filePath, 0x0, sizeof(filePath));
            strcpy(filePath, gifFile.path());
            currentGifPath = String(filePath); // Save the current file path

            // Show the GIF
            ShowGIF(filePath);

            // If looping is enabled, continue playing the same GIF
            if (loopGifEnabled) {
              continue; // Restart the loop for the same GIF
            }
          }
          // Handle Image files
          else if (isImageFile(fileName)) {
            memset(imageFilePath, 0x0, sizeof(imageFilePath));
            strcpy(imageFilePath, gifFile.path());
            currentGifPath = String(imageFilePath); // Save the current file path
            currentImagePath = String(imageFilePath);

            // Show the Image
            ShowImage(imageFilePath);

            // Wait for the image display duration
            while (millis() - imageDisplayStart < imageDisplayDuration) {
              delay(100);
            }
          }
        }

        if (!loopGifEnabled) {
          // If looping is disabled, move to the next media file (GIF or Image)
          gifFile.close();      // Close the current file

          // Find the next media file
          do {
            gifFile = root.openNextFile();
          } while (gifFile && !isGifFile(String(gifFile.path())) && !isImageFile(String(gifFile.path())));

          // If no more media files, reset to the first media file
          if (!gifFile) {
            root.close(); // Close the root directory
            root = FILESYSTEM.open(gifDir); // Reopen the root directory
            do {
              gifFile = root.openNextFile();
            } while (gifFile && !isGifFile(String(gifFile.path())) && !isImageFile(String(gifFile.path())));
          }
        } else {
          break; // Exit the loop if looping is disabled
        }
      }

      root.close(); // Close the root directory
    } else {
      // If no media files found, still update display layers for clock/scrolling text
      UpdateDisplayLayers();
      // Blend the layers (like the original does in ShowGIF)
      gfx_layer_bg.dim(150);
      gfx_layer_fg.dim(255);
      gfx_compositor.Blend(gfx_layer_bg, gfx_layer_fg);
    }

    delay(10); // Pause before restarting (back to original timing)
  }
}