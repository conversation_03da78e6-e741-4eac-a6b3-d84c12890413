# ESP32 Divoom Clone - Enhanced Version

An enhanced ESP32-based RGB LED matrix display system that supports GIFs, images (JPEG/PNG/BMP), scrolling text, and clock display with a web-based control interface.

## 🚀 Features

### Core Features
- **Multi-format Media Support**: GIF, JPEG, PNG, BMP files
- **64x64 RGB LED Matrix Display**: Full-color display with HUB75 interface
- **Web-based Control Interface**: Upload files and control display settings
- **Scrolling Text Overlay**: Customizable text with font size and speed control
- **Digital Clock Display**: Time display with blinking colon separator
- **File Management**: Upload, delete, and organize media files via web interface

### Enhanced Features (Beyond Original)
- **Image Format Support**: JPEG, PNG, BMP (original only supported GIFs)
- **Improved Color Quality**: Enhanced color processing and gamma correction
- **Layer Compositing**: Proper blending of background media and foreground text/clock
- **Direct Rendering**: Bypass layer system for optimal image quality
- **Dynamic Configuration**: Adjust display settings via web API
- **File Type Detection**: Automatic detection and handling of different media formats

## 🏗️ System Architecture

### Hardware Components
- **ESP32-S3 Development Board**
- **64x64 RGB LED Matrix Panel** (HUB75 interface)
- **Power Supply** (5V, adequate amperage for LED matrix)
- **MicroSD Card** (optional, for additional storage)

### Software Architecture

```mermaid
graph TB
    subgraph "ESP32 Firmware"
        A[Main Loop] --> B[File Iterator]
        B --> C{File Type?}
        C -->|GIF| D[GIF Player]
        C -->|Image| E[Image Display]
        C -->|Unknown| F[Skip File]
        
        D --> G[GIFDraw Callback]
        E --> H[Image Decoder]
        
        G --> I[UpdateDisplayLayers]
        H --> I
        
        I --> J[Layer Compositor]
        J --> K[RGB Matrix Display]
        
        subgraph "Display Layers"
            L[Background Layer<br/>Media Content]
            M[Foreground Layer<br/>Text & Clock]
        end
        
        I --> L
        I --> M
        L --> J
        M --> J
    end
    
    subgraph "Web Interface"
        N[Upload Files] --> O[SPIFFS Storage]
        P[Control Settings] --> Q[Web Server]
        Q --> R[Update Variables]
    end
    
    O --> B
    R --> I
```

## 🔄 Media Processing Flow

### File Detection and Iteration

```mermaid
sequenceDiagram
    participant ML as Main Loop
    participant FI as File Iterator
    participant FS as SPIFFS
    participant GP as GIF Player
    participant IP as Image Processor
    participant DL as Display Layers

    ML->>FI: Start file iteration
    FI->>FS: Open directory
    FS->>FI: Return file list
    
    loop For each file
        FI->>FI: Check file extension
        alt GIF file
            FI->>GP: ShowGIF()
            GP->>DL: GIFDraw callback
        else Image file (JPEG/PNG/BMP)
            FI->>IP: ShowImage()
            IP->>DL: Image render callback
        else Unknown file
            FI->>FI: Skip file
        end
        
        DL->>DL: UpdateDisplayLayers()
        DL->>DL: Blend layers
        DL->>ML: Display updated
        
        alt loopGifEnabled = false
            FI->>FI: Move to next file
        else loopGifEnabled = true
            FI->>FI: Repeat current file
        end
    end
```

## 📁 File Structure

```
esp32_divoom_clone/
├── esp32_divoom_clone.ino     # Main Arduino sketch
├── config.h                   # Hardware and system configuration
├── display.cpp/.h             # Display initialization and layer management
├── gif.cpp/.h                 # GIF playback and display layer updates
├── image.cpp/.h               # Image processing (JPEG/PNG/BMP)
├── webserver.cpp/.h           # Web server and API endpoints
├── webpages.cpp/.h            # HTML/CSS/JS for web interface
├── config.cpp/.h              # Configuration management
└── data/
    └── index.html             # Web interface (served from SPIFFS)
```

## 🎨 Display Layer System

The system uses a dual-layer approach for compositing different types of content:

### Layer Architecture

```mermaid
graph LR
    subgraph "Display Layers"
        A[Background Layer<br/>gfx_layer_bg] --> C[Layer Compositor<br/>gfx_compositor]
        B[Foreground Layer<br/>gfx_layer_fg] --> C
    end
    
    subgraph "Content Types"
        D[GIF Frames] --> A
        E[JPEG Images] --> A
        F[PNG Images] --> A
        G[BMP Images] --> A
        H[Scrolling Text] --> B
        I[Digital Clock] --> B
    end
    
    C --> J[RGB Matrix Display<br/>dma_display]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
```

### Layer Blending Process

1. **Background Layer** (`gfx_layer_bg`): Contains media content (GIFs, images)
2. **Foreground Layer** (`gfx_layer_fg`): Contains text and clock overlays
3. **Compositor**: Blends layers with configurable opacity
4. **Display Output**: Final composited image sent to RGB matrix

## 🖼️ Image Processing Pipeline

### Supported Formats and Processing

```mermaid
flowchart TD
    A[Upload File] --> B{File Type?}
    
    B -->|.gif| C[AnimatedGIF Library]
    B -->|.jpg/.jpeg| D[TJpg_Decoder Library]
    B -->|.png| E[PNGdec Library]
    B -->|.bmp| F[Custom BMP Decoder]
    
    C --> G[GIFDraw Callback]
    D --> H[jpegRender Callback]
    E --> I[pngDraw Callback]
    F --> J[Direct Pixel Drawing]
    
    G --> K[Background Layer]
    H --> K
    I --> K
    J --> K
    
    K --> L[UpdateDisplayLayers]
    L --> M[Add Text/Clock Overlay]
    M --> N[Layer Compositor]
    N --> O[RGB Matrix Display]
    
    style C fill:#ff9800
    style D fill:#4caf50
    style E fill:#2196f3
    style F fill:#9c27b0
```

## ⚙️ Configuration Options

### Display Settings
- **Panel Resolution**: 64x64 pixels (configurable)
- **Color Depth**: RGB565 (16-bit color)
- **Brightness**: 0-255 (adjustable via web interface)
- **Refresh Rate**: Optimized for smooth animation

### Media Settings
- **Image Display Duration**: 15 seconds (configurable)
- **GIF Loop Control**: Enable/disable looping
- **File Cycling**: Automatic progression through media files
- **Supported Formats**: GIF, JPEG, PNG, BMP

### Text and Clock Settings
- **Scrolling Text**: Customizable message, font size (1-4), speed (1-150)
- **Digital Clock**: 12-hour format with blinking colon
- **Text Color**: RGB color selection
- **Overlay Mode**: Text and clock overlay on media content

## 🌐 Web Interface API

### File Management
- `POST /upload` - Upload media files
- `GET /api/files` - List all files
- `DELETE /api/files?filename=X` - Delete specific file

### Display Control
- `GET /toggleGIF?state=on/off` - Enable/disable GIF playback
- `GET /toggleLoopGif?state=on/off` - Enable/disable GIF looping
- `GET /toggleClock?state=on/off` - Enable/disable clock display
- `GET /toggleScrollText?state=on/off` - Enable/disable scrolling text

### Settings
- `GET /updateScrollText?text=X&fontSize=Y&speed=Z` - Update scrolling text
- `GET /setImageDuration?duration=X` - Set image display duration (ms)
- `GET /setBrightness?brightness=X` - Set display brightness (0-255)

### Debug and Testing
- `GET /listFiles` - Debug endpoint to list all files with types
- `GET /testColors` - Display color test pattern
- `GET /reboot` - Restart the ESP32

## 🔧 Technical Implementation Details

### Color Processing
- **RGB565 Format**: 16-bit color depth for efficient memory usage
- **Byte Swapping**: Enabled for correct color representation on ESP32
- **Gamma Correction**: Optional color enhancement for better visual quality
- **Direct Rendering**: Bypass layer system for optimal image quality

### Memory Management
- **SPIFFS Storage**: File system for media and web interface files
- **Layer Buffers**: Separate buffers for background and foreground content
- **Streaming Decode**: Images decoded directly to display buffers
- **Memory Optimization**: Efficient handling of large images on limited RAM

### Performance Optimizations
- **Hardware Acceleration**: DMA-based display updates
- **Callback-based Rendering**: Efficient pixel-by-pixel drawing
- **Optimized Blending**: Fast layer composition
- **Minimal Memory Footprint**: Stream processing without large buffers

## 🚀 Getting Started

### Hardware Setup
1. Connect ESP32-S3 to 64x64 RGB LED matrix via HUB75 connector
2. Ensure adequate 5V power supply for LED matrix
3. Connect ESP32 to WiFi network

### Software Setup
1. Install required Arduino libraries:
   - ESP32-HUB75-MatrixPanel-I2S-DMA
   - AnimatedGIF
   - TJpg_Decoder
   - PNGdec
   - ESPAsyncWebServer
   - AsyncTCP

2. Configure WiFi credentials in `config.cpp`
3. Upload firmware to ESP32-S3
4. Upload web interface files to SPIFFS
5. Access web interface via ESP32's IP address

### Usage
1. **Upload Media**: Use web interface to upload GIF/image files
2. **Configure Display**: Adjust brightness, text, clock settings
3. **Control Playback**: Enable/disable features as needed
4. **Monitor Status**: Check serial output for debug information

## 🔍 Troubleshooting

### Common Issues
- **Upload Failed**: Check file size limits and SPIFFS space
- **Poor Image Quality**: Ensure proper color format and brightness settings
- **File Not Playing**: Verify file format and check debug output
- **Display Issues**: Check hardware connections and power supply

### Debug Endpoints
- `/listFiles` - View all files and their detected types
- `/testColors` - Test basic color display functionality
- Serial monitor output for detailed debugging information

## 📈 Performance Characteristics

- **File Formats**: GIF (animated), JPEG/PNG/BMP (static)
- **Display Resolution**: 64x64 pixels
- **Color Depth**: 65,536 colors (RGB565)
- **Animation Frame Rate**: Depends on GIF frame timing
- **Image Display Duration**: 15 seconds (configurable)
- **Text Scroll Speed**: 1-150 (configurable)
- **Memory Usage**: Optimized for ESP32 constraints

---

*Based on the original HUB75-Pixel-Art-Display project with significant enhancements for multi-format media support, improved color quality, and advanced web interface.*
