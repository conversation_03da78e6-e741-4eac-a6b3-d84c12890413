<!DOCTYPE HTML>
<html lang="en">
<head>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta charset="UTF-8">
  <title>HUB75 Pixel Art Display Control</title>
  <style>
    body {
      background-color: #121212;
      color: white;
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
      user-select: none;
    }

    .header {
      text-align: center;
      margin-bottom: 30px;
    }

    .header h1 {
      color: #FFD65C;
      margin-bottom: 10px;
      font-size: 2.5em;
    }

    .info-bar {
      background-color: #333;
      border-radius: 10px;
      padding: 15px;
      margin-bottom: 20px;
      width: 100%;
      max-width: 600px;
      text-align: center;
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 15px;
      margin: 15px 0;
    }

    .info-item {
      background-color: #444;
      padding: 10px;
      border-radius: 5px;
    }

    .info-label {
      font-size: 12px;
      color: #aaa;
      margin-bottom: 5px;
    }

    .info-value {
      font-size: 16px;
      font-weight: bold;
      color: #FFD65C;
    }

    .control-section {
      background-color: #333;
      border-radius: 10px;
      padding: 20px;
      margin-bottom: 20px;
      width: 100%;
      max-width: 600px;
    }

    .control-section h3 {
      margin-top: 0;
      color: #FFD65C;
      border-bottom: 2px solid #555;
      padding-bottom: 10px;
    }

    .controls-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
    }

    .btn {
      background-color: #555;
      border: none;
      color: white;
      padding: 12px 20px;
      font-size: 16px;
      cursor: pointer;
      border-radius: 5px;
      transition: all 0.2s;
      margin: 5px;
      min-width: 100px;
    }

    .btn:hover {
      background-color: #666;
      transform: translateY(-2px);
    }

    .btn:active {
      background-color: #777;
      transform: translateY(0);
    }

    .btn.primary {
      background-color: #FFD65C;
      color: #003249;
      font-weight: bold;
    }

    .btn.primary:hover {
      background-color: #FFC107;
    }

    .btn.danger {
      background-color: #dc3545;
    }

    .btn.danger:hover {
      background-color: #c82333;
    }

    .btn.success {
      background-color: #28a745;
    }

    .btn.success:hover {
      background-color: #218838;
    }

    .checkbox-group {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin: 15px 0;
    }

    .checkbox-group label {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      background-color: #444;
      border-radius: 5px;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .checkbox-group label:hover {
      background-color: #555;
    }

    .checkbox-group input[type="checkbox"] {
      transform: scale(1.2);
      accent-color: #FFD65C;
    }

    .color-controls {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      align-items: center;
      margin: 15px 0;
    }

    .color-input-group {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 5px;
    }

    .color-controls input[type="number"] {
      width: 70px;
      padding: 8px;
      border: 2px solid #555;
      background-color: #222;
      color: white;
      border-radius: 5px;
      text-align: center;
      font-size: 14px;
    }

    .color-controls input[type="number"]:focus {
      border-color: #FFD65C;
      outline: none;
    }

    .slider-container {
      margin: 20px 0;
    }

    .slider-label {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      font-size: 16px;
      font-weight: bold;
    }

    .slider {
      -webkit-appearance: none;
      width: 100%;
      height: 8px;
      background: #555;
      outline: none;
      border-radius: 4px;
      transition: background 0.2s;
    }

    .slider:hover {
      background: #666;
    }

    .slider::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 24px;
      height: 24px;
      background: #FFD65C;
      cursor: pointer;
      border-radius: 50%;
      box-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .slider::-moz-range-thumb {
      width: 24px;
      height: 24px;
      background: #FFD65C;
      cursor: pointer;
      border-radius: 50%;
      border: none;
      box-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .text-input-group {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      align-items: center;
      margin: 15px 0;
    }

    .text-input-group input[type="text"], 
    .text-input-group input[type="number"], 
    .text-input-group select {
      padding: 10px;
      border: 2px solid #555;
      background-color: #222;
      color: white;
      border-radius: 5px;
      font-size: 14px;
    }

    .text-input-group input[type="text"] {
      flex: 1;
      min-width: 200px;
    }

    .text-input-group input:focus, 
    .text-input-group select:focus {
      border-color: #FFD65C;
      outline: none;
    }

    .button-group {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      justify-content: center;
      margin: 20px 0;
    }

    #fileList {
      margin-top: 20px;
      width: 100%;
      max-width: 800px;
    }

    #fileList table {
      width: 100%;
      border-collapse: collapse;
      background-color: #333;
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 4px 6px rgba(0,0,0,0.3);
    }

    #fileList th, #fileList td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid #555;
    }

    #fileList th {
      background-color: #444;
      font-weight: bold;
      color: #FFD65C;
    }

    #fileList img {
      border-radius: 5px;
      border: 2px solid #555;
    }

    .connection-status {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 20px;
      border-radius: 25px;
      font-weight: bold;
      z-index: 1000;
      box-shadow: 0 2px 10px rgba(0,0,0,0.3);
      transition: all 0.3s;
    }

    .connected {
      background-color: #28a745;
      color: white;
    }

    .disconnected {
      background-color: #dc3545;
      color: white;
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.7; }
      100% { opacity: 1; }
    }

    .upload-area {
      border: 3px dashed #555;
      border-radius: 10px;
      padding: 40px;
      text-align: center;
      margin: 20px 0;
      background-color: #222;
      transition: all 0.3s;
    }

    .upload-area:hover {
      border-color: #FFD65C;
      background-color: #333;
    }

    .progress-container {
      margin: 20px 0;
    }

    .progress-bar {
      width: 100%;
      height: 25px;
      background-color: #444;
      border-radius: 15px;
      overflow: hidden;
      box-shadow: inset 0 2px 4px rgba(0,0,0,0.3);
    }

    .progress-bar progress {
      width: 100%;
      height: 100%;
      border: none;
      border-radius: 15px;
    }

    .progress-bar progress::-webkit-progress-bar {
      background-color: #444;
      border-radius: 15px;
    }

    .progress-bar progress::-webkit-progress-value {
      background: linear-gradient(90deg, #FFD65C, #FFC107);
      border-radius: 15px;
    }

    .status-message {
      margin-top: 10px;
      padding: 10px;
      border-radius: 5px;
      text-align: center;
      font-weight: bold;
    }

    .notification {
      position: fixed;
      top: 80px;
      right: 20px;
      padding: 15px 20px;
      border-radius: 5px;
      color: white;
      font-weight: bold;
      z-index: 1001;
      box-shadow: 0 4px 6px rgba(0,0,0,0.3);
      animation: slideIn 0.3s ease-out;
    }

    .notification.success {
      background-color: #28a745;
    }

    .notification.error {
      background-color: #dc3545;
    }

    @keyframes slideIn {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }

    @media (max-width: 768px) {
      .controls-grid {
        grid-template-columns: 1fr;
      }
      
      .checkbox-group {
        flex-direction: column;
        gap: 10px;
      }
      
      .color-controls {
        flex-direction: column;
        align-items: stretch;
      }
      
      .text-input-group {
        flex-direction: column;
      }
      
      .text-input-group input[type="text"] {
        min-width: unset;
      }
    }
  </style>
</head>
<body>
  <div class="connection-status disconnected" id="status">
    Connecting...
  </div>

  <div class="header">
    <h1>🎨 HUB75 Pixel Art Display</h1>
    <div class="info-bar">
      <div><strong>Firmware:</strong> <span id="firmware">Loading...</span></div>
      <div class="info-grid">
        <div class="info-item">
          <div class="info-label">FREE STORAGE</div>
          <div class="info-value" id="freeFlash">Loading...</div>
        </div>
        <div class="info-item">
          <div class="info-label">USED STORAGE</div>
          <div class="info-value" id="usedFlash">Loading...</div>
        </div>
        <div class="info-item">
          <div class="info-label">TOTAL STORAGE</div>
          <div class="info-value" id="totalFlash">Loading...</div>
        </div>
      </div>
    </div>
  </div>

  <div class="controls-grid">
    <div class="control-section">
      <h3>🎮 Display Controls</h3>
      <div class="checkbox-group">
        <label>
          <input type="checkbox" id="gifToggle" checked> 
          <span>Play GIFs</span>
        </label>
        <label>
          <input type="checkbox" id="loopGifToggle" checked> 
          <span>Loop GIFs</span>
        </label>
        <label>
          <input type="checkbox" id="clockToggle" checked> 
          <span>Show Clock</span>
        </label>
        <label>
          <input type="checkbox" id="scrollTextToggle"> 
          <span>Scrolling Text</span>
        </label>
      </div>
    </div>

    <div class="control-section">
      <h3>🎨 Color Settings</h3>
      <div class="color-controls">
        <div class="color-input-group">
          <label>Red</label>
          <input type="number" id="r" min="0" max="255" value="255">
        </div>
        <div class="color-input-group">
          <label>Green</label>
          <input type="number" id="g" min="0" max="255" value="255">
        </div>
        <div class="color-input-group">
          <label>Blue</label>
          <input type="number" id="b" min="0" max="255" value="255">
        </div>
        <button class="btn primary" onclick="setColor()">Set Color</button>
      </div>
    </div>
  </div>

  <div class="control-section">
    <h3>💬 Scrolling Text</h3>
    <div class="text-input-group">
      <input type="text" id="scrollText" placeholder="Enter your scrolling text here">
      <select id="fontSizeToggle">
        <option value="1">Small</option>
        <option value="2" selected>Normal</option>
        <option value="3">Big</option>
        <option value="4">Huge</option>
      </select>
      <input type="number" id="scrollSpeed" min="1" max="150" value="50" placeholder="Speed (1-150)">
      <button class="btn primary" onclick="sendScrollTextData()">Update Text</button>
    </div>
  </div>

  <div class="control-section">
    <h3>🔆 Brightness Control</h3>
    <div class="slider-container">
      <div class="slider-label">
        <span>Brightness</span>
        <span id="textSliderValue">128</span>
      </div>
      <input type="range" class="slider" id="pwmSlider" min="0" max="255" value="128" step="1">
    </div>
  </div>

  <div class="control-section">
    <h3>📁 File Management</h3>
    <div class="button-group">
      <button class="btn" onclick="listFilesButton()">📋 List Files</button>
      <button class="btn" onclick="showUploadButtonFancy()">📤 Upload File</button>
      <button class="btn danger" onclick="rebootButton()">🔄 Reboot</button>
      <button class="btn" onclick="logoutButton()">🚪 Logout</button>
    </div>
  </div>

  <div id="fileList"></div>

  <script>
    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
      setupEventListeners();
      updateConnectionStatus(false);
      loadSystemInfo();
    });

    function setupEventListeners() {
      // Checkbox event listeners
      document.getElementById('gifToggle').addEventListener('change', function() {
        toggleGIF(this);
      });
      
      document.getElementById('loopGifToggle').addEventListener('change', function() {
        toggleLoopGif(this);
      });
      
      document.getElementById('clockToggle').addEventListener('change', function() {
        toggleClock(this);
      });
      
      document.getElementById('scrollTextToggle').addEventListener('change', function() {
        toggleScrollText(this);
      });

      // Slider event listener
      document.getElementById('pwmSlider').addEventListener('input', function() {
        updateSliderPWM(this);
      });
    }

    function updateConnectionStatus(connected) {
      const statusEl = document.getElementById('status');
      if (connected) {
        statusEl.className = 'connection-status connected';
        statusEl.textContent = '✅ Connected';
      } else {
        statusEl.className = 'connection-status disconnected';
        statusEl.textContent = '❌ Disconnected';
      }
    }

    function showNotification(message, isError = false) {
      // Remove existing notifications
      const existing = document.querySelectorAll('.notification');
      existing.forEach(n => n.remove());

      const notification = document.createElement('div');
      notification.className = `notification ${isError ? 'error' : 'success'}`;
      notification.textContent = message;
      document.body.appendChild(notification);
      
      setTimeout(() => {
        notification.remove();
      }, 3000);
    }

    function updateSliderPWM(element) {
      const sliderValue = element.value;
      document.getElementById("textSliderValue").textContent = sliderValue;
      
      fetch(`/slider?value=${sliderValue}`)
        .then(response => {
          if (response.ok) updateConnectionStatus(true);
        })
        .catch(error => {
          updateConnectionStatus(false);
          console.error('Error:', error);
        });
    }

    function setColor() {
      const r = document.getElementById("r").value;
      const g = document.getElementById("g").value;
      const b = document.getElementById("b").value;

      fetch(`/setColor?r=${r}&g=${g}&b=${b}`)
        .then(response => response.text())
        .then(data => {
          updateConnectionStatus(true);
          showNotification("Color updated successfully!");
        })
        .catch(error => {
          updateConnectionStatus(false);
          showNotification("Failed to update color.", true);
        });
    }

    function toggleGIF(checkbox) {
      const state = checkbox.checked ? "on" : "off";
      fetch(`/toggleGIF?state=${state}`)
        .then(response => response.text())
        .then(data => {
          updateConnectionStatus(true);
          showNotification("GIF playback state updated!");
        })
        .catch(error => {
          updateConnectionStatus(false);
          showNotification("Failed to update GIF playback state.", true);
        });
    }

    function toggleLoopGif(checkbox) {
      const state = checkbox.checked ? "on" : "off";
      fetch(`/toggleLoopGif?state=${state}`)
        .then(response => response.text())
        .then(data => {
          updateConnectionStatus(true);
          showNotification(`Loop GIF ${state === "on" ? "enabled" : "disabled"}`);
        })
        .catch(error => {
          updateConnectionStatus(false);
          showNotification("Failed to toggle Loop GIF.", true);
        });
    }

    function toggleClock(checkbox) {
      const state = checkbox.checked ? "on" : "off";

      if (state === "on") {
        document.getElementById("scrollTextToggle").checked = false;
      }

      fetch(`/toggleClock?state=${state}`)
        .then(response => response.text())
        .then(data => {
          updateConnectionStatus(true);
          showNotification("Clock state updated!");
        })
        .catch(error => {
          updateConnectionStatus(false);
          showNotification("Failed to update clock state.", true);
        });
    }

    function toggleScrollText(checkbox) {
      const isEnabled = checkbox.checked;
      const scrollTextInput = document.getElementById("scrollText");
      const fontSizeToggle = document.getElementById("fontSizeToggle");
      const scrollSpeedInput = document.getElementById("scrollSpeed");

      scrollTextInput.disabled = !isEnabled;
      fontSizeToggle.disabled = !isEnabled;
      scrollSpeedInput.disabled = !isEnabled;

      if (isEnabled) {
        document.getElementById("clockToggle").checked = false;
      }

      fetch(`/toggleScrollText?state=${isEnabled ? "on" : "off"}`)
        .then(response => response.text())
        .then(data => {
          updateConnectionStatus(true);
          showNotification(`Scrolling text ${isEnabled ? "enabled" : "disabled"}`);
        })
        .catch(error => {
          updateConnectionStatus(false);
          showNotification("Failed to toggle scrolling text.", true);
        });
    }

    function sendScrollTextData() {
      const text = document.getElementById("scrollText").value;
      const fontSize = document.getElementById("fontSizeToggle").value;
      const speed = document.getElementById("scrollSpeed").value;

      fetch(`/updateScrollText?text=${encodeURIComponent(text)}&fontSize=${fontSize}&speed=${speed}`)
        .then(response => response.text())
        .then(data => {
          updateConnectionStatus(true);
          showNotification("Scrolling text updated!");
        })
        .catch(error => {
          updateConnectionStatus(false);
          showNotification("Failed to update scrolling text.", true);
        });
    }

    function loadSystemInfo() {
      fetch('/api/info')
        .then(response => response.json())
        .then(data => {
          updateConnectionStatus(true);
          document.getElementById("firmware").textContent = data.firmware;
          document.getElementById("freeFlash").textContent = data.freeFlash;
          document.getElementById("usedFlash").textContent = data.usedFlash;
          document.getElementById("totalFlash").textContent = data.totalFlash;

          // Update slider value
          const sliderValue = data.sliderValue;
          document.getElementById("textSliderValue").textContent = sliderValue;
          document.getElementById("pwmSlider").value = sliderValue;
        })
        .catch(error => {
          updateConnectionStatus(false);
          console.error('Error loading system info:', error);
        });
    }

    function listFilesButton() {
      fetch('/api/files')
        .then(response => response.json())
        .then(data => {
          updateConnectionStatus(true);

          // Create HTML table from JSON data
          let html = '<div class="control-section">';
          html += '<h3>📁 Files</h3>';
          html += '<table><tr><th>Name</th><th>Size</th><th>Actions</th></tr>';

          data.files.forEach(file => {
            html += '<tr>';
            html += `<td>${file.name}</td>`;
            html += `<td>${file.size}</td>`;
            html += '<td>';
            html += `<button onclick="downloadDeleteButton('${file.name}', 'play')">Play</button>`;
            html += `<button onclick="downloadDeleteButton('${file.name}', 'download')">Download</button>`;
            html += `<button onclick="downloadDeleteButton('${file.name}', 'delete')">Delete</button>`;
            html += '</td></tr>';
          });

          html += '</table>';

          // Add pagination
          const pagination = data.pagination;
          if (pagination.totalPages > 1) {
            html += `<p>Page ${pagination.currentPage} of ${pagination.totalPages}</p>`;
            html += '<div class="button-group">';

            if (pagination.currentPage > 1) {
              html += `<button onclick="listFilesPage(${pagination.currentPage - 1})">Previous</button>`;
            }

            for (let i = 1; i <= pagination.totalPages; i++) {
              if (i === pagination.currentPage) {
                html += `<button disabled>${i}</button>`;
              } else {
                html += `<button onclick="listFilesPage(${i})">${i}</button>`;
              }
            }

            if (pagination.currentPage < pagination.totalPages) {
              html += `<button onclick="listFilesPage(${pagination.currentPage + 1})">Next</button>`;
            }

            html += '</div>';
          }

          html += '</div>';
          document.getElementById("fileList").innerHTML = html;
        })
        .catch(error => {
          updateConnectionStatus(false);
          showNotification("Failed to load files.", true);
          console.error('Error loading files:', error);
        });
    }

    function listFilesPage(page) {
      fetch(`/api/files?page=${page}`)
        .then(response => response.json())
        .then(data => {
          // Same rendering logic as listFilesButton
          // This could be refactored to avoid duplication
          let html = '<div class="control-section">';
          html += '<h3>📁 Files</h3>';
          html += '<table><tr><th>Name</th><th>Size</th><th>Actions</th></tr>';

          data.files.forEach(file => {
            html += '<tr>';
            html += `<td>${file.name}</td>`;
            html += `<td>${file.size}</td>`;
            html += '<td>';
            html += `<button onclick="downloadDeleteButton('${file.name}', 'play')">Play</button>`;
            html += `<button onclick="downloadDeleteButton('${file.name}', 'download')">Download</button>`;
            html += `<button onclick="downloadDeleteButton('${file.name}', 'delete')">Delete</button>`;
            html += '</td></tr>';
          });

          html += '</table>';

          // Add pagination
          const pagination = data.pagination;
          if (pagination.totalPages > 1) {
            html += `<p>Page ${pagination.currentPage} of ${pagination.totalPages}</p>`;
            html += '<div class="button-group">';

            if (pagination.currentPage > 1) {
              html += `<button onclick="listFilesPage(${pagination.currentPage - 1})">Previous</button>`;
            }

            for (let i = 1; i <= pagination.totalPages; i++) {
              if (i === pagination.currentPage) {
                html += `<button disabled>${i}</button>`;
              } else {
                html += `<button onclick="listFilesPage(${i})">${i}</button>`;
              }
            }

            if (pagination.currentPage < pagination.totalPages) {
              html += `<button onclick="listFilesPage(${pagination.currentPage + 1})">Next</button>`;
            }

            html += '</div>';
          }

          html += '</div>';
          document.getElementById("fileList").innerHTML = html;
        })
        .catch(error => {
          updateConnectionStatus(false);
          showNotification("Failed to load files.", true);
        });
    }

    function showUploadButtonFancy() {
      const uploadForm = `
        <div class="control-section">
          <h3>📤 Upload GIF File</h3>
          <div class="upload-area">
            <form id="upload_form" enctype="multipart/form-data" method="post">
              <p>📁 Select a GIF file to upload</p>
              <input type="file" name="file1" id="file1" accept=".gif" style="margin-bottom: 15px;">
              <div class="progress-container">
                <div class="progress-bar">
                  <progress id="progressBar" value="0" max="100"></progress>
                </div>
                <div id="upload_status" class="status-message">Select a GIF file to upload</div>
              </div>
            </form>
          </div>
        </div>
      `;
      document.getElementById("fileList").innerHTML = uploadForm;
      
      document.getElementById('file1').addEventListener('change', uploadFile);
    }

    function uploadFile() {
      const file = document.getElementById("file1").files[0];
      if (!file) return;

      const formdata = new FormData();
      formdata.append("file1", file);
      
      const xhr = new XMLHttpRequest();
      
      xhr.upload.addEventListener("progress", function(event) {
        if (event.lengthComputable) {
          const percent = Math.round((event.loaded / event.total) * 100);
          document.getElementById("progressBar").value = percent;
          document.getElementById("upload_status").textContent = `Uploading... ${percent}%`;
        }
      });
      
      xhr.addEventListener("load", function() {
        if (xhr.status === 200) {
          document.getElementById("upload_status").textContent = "Upload Complete! 🎉";
          updateConnectionStatus(true);
          showNotification("File uploaded successfully!");
          setTimeout(listFilesButton, 1000);
        } else {
          document.getElementById("upload_status").textContent = "Upload Failed ❌";
          updateConnectionStatus(false);
          showNotification("Upload failed.", true);
        }
      });
      
      xhr.addEventListener("error", function() {
        document.getElementById("upload_status").textContent = "Upload Error ❌";
        updateConnectionStatus(false);
        showNotification("Upload error occurred.", true);
      });
      
      xhr.open("POST", "/");
      xhr.send(formdata);
    }

    function downloadDeleteButton(filename, action) {
      const url = `/file?name=${filename}&action=${action}`;
      
      if (action === 'delete') {
        if (confirm(`🗑️ Are you sure you want to delete ${filename}?`)) {
          fetch(url)
            .then(response => response.text())
            .then(data => {
              updateConnectionStatus(true);
              showNotification("File deleted successfully!");
              listFilesButton();
            })
            .catch(error => {
              updateConnectionStatus(false);
              showNotification("Failed to delete file.", true);
            });
        }
      } else if (action === 'download') {
        window.open(url, '_blank');
      } else if (action === 'play') {
        fetch(url)
          .then(response => response.text())
          .then(data => {
            updateConnectionStatus(true);
            showNotification(`🎬 Playing GIF: ${filename}`);
          })
          .catch(error => {
            updateConnectionStatus(false);
            showNotification("Failed to play GIF.", true);
          });
      }
    }

    function logoutButton() {
      fetch('/logout')
        .then(() => {
          showNotification("Logging out...");
          setTimeout(() => window.location.href = '/logged-out', 1000);
        });
    }

    function rebootButton() {
      if (confirm('🔄 Are you sure you want to reboot the device?')) {
        fetch('/reboot')
          .then(() => {
            showNotification("Rebooting device...");
            setTimeout(() => window.location.href = '/reboot', 1000);
          });
      }
    }

    // Legacy functions for compatibility
    function logoutButton() {
      var xhr = new XMLHttpRequest();
      xhr.open("GET", "/logout", true);
      xhr.send();
      setTimeout(function(){ window.open("/logged-out","_self"); }, 1000);
    }

    function rebootButton() {
      document.getElementById("fileList").innerHTML = "Invoking Reboot ...";
      var xhr = new XMLHttpRequest();
      xhr.open("GET", "/reboot", false);
      xhr.send();
      window.open("/reboot","_self");
    }

    function listFilesButton() {
      var xmlhttp = new XMLHttpRequest();
      xmlhttp.open("GET", "/listfiles", false);
      xmlhttp.send();
      document.getElementById("fileList").innerHTML = xmlhttp.responseText;
    }

    window.currentPage = 1;

    window.navigatePage = function (page) {
      console.log("Navigating to page:", page);
      currentPage = page;

      fetch(`/list?page=${page}`)
        .then(response => response.text())
        .then(data => {
          console.log("Received data:", data);
          document.getElementById("fileList").innerHTML = data;

          const buttons = document.querySelectorAll("#fileList button");
          buttons.forEach(button => {
            console.log("Button found:", button.outerHTML);
          });
        })
        .catch(error => {
          console.error("Error fetching page:", error);
        });
    };

    function _(el) {
      return document.getElementById(el);
    }

    function uploadFile() {
      var file = _("file1").files[0];
      var formdata = new FormData();
      formdata.append("file1", file);
      var ajax = new XMLHttpRequest();
      ajax.upload.addEventListener("progress", progressHandler, false);
      ajax.addEventListener("load", completeHandler, false);
      ajax.addEventListener("error", errorHandler, false);
      ajax.addEventListener("abort", abortHandler, false);
      ajax.open("POST", "/");
      ajax.send(formdata);
    }

    function progressHandler(event) {
      _("loaded_n_total").innerHTML = "Uploaded " + event.loaded + " bytes";
      var percent = (event.loaded / event.total) * 100;
      _("progressBar").value = Math.round(percent);
      _("status").innerHTML = Math.round(percent) + "% uploaded... please wait";
      if (percent >= 100) {
        _("status").innerHTML = "Please wait, writing file to filesystem";
      }
    }

    function completeHandler(event) {
      _("status").innerHTML = "Upload Complete";
      _("progressBar").value = 0;
      var xmlhttp = new XMLHttpRequest();
      xmlhttp.open("GET", "/listfiles", false);
      xmlhttp.send();
      document.getElementById("status").innerHTML = "File Uploaded";
      document.getElementById("fileList").innerHTML = xmlhttp.responseText;
    }

    function errorHandler(event) {
      _("status").innerHTML = "Upload Failed";
    }

    function abortHandler(event) {
      _("status").innerHTML = "Upload Aborted";
    }
  </script>
</body>
</html>