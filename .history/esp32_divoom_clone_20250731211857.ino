//By mzashh https://github.com/mzashh
//Organized version with separate header files

#include "config.h"
#include <SPIFFS.h>
#include <AnimatedGIF.h>
#include <ESP32-HUB75-MatrixPanel-I2S-DMA.h>
#include <WiFi.h>
#include <AsyncTCP.h>
#include <GFX_Layer.hpp>
#include <ESPAsyncWebServer.h>
#include "time.h"
#include "display.h"
#include "gif.h" 
#include "webserver.h"

void setup() {
  Serial.begin(115200);
  Serial.print("Firmware: "); Serial.println(FIRMWARE_VERSION);
  Serial.println("Booting ...");

  // Initialize filesystem
  Serial.println("Mounting SPIFFS ...");
  if (!SPIFFS.begin(true)) {
    Serial.println("ERROR: Cannot mount SPIFFS, Rebooting");
    rebootESP("ERROR: Cannot mount SPIFFS, Rebooting");
  }

  // Initialize display
  setupDisplay();

  // Print filesystem info
  Serial.print("Flash Free: "); Serial.println(humanReadableSize((SPIFFS.totalBytes() - SPIFFS.usedBytes())));
  Serial.print("Flash Used: "); Serial.println(humanReadableSize(SPIFFS.usedBytes()));
  Serial.print("Flash Total: "); Serial.println(humanReadableSize(SPIFFS.totalBytes()));

  // Initialize config
  initializeConfig();

  // Setup WiFi
  setupWiFi();

  // Setup web server
  setupWebServer();

  // Setup time
  configTime(gmtOffset_sec, daylightOffset_sec, ntpServer);

  // Show startup info
  showStartupInfo();

  // Initialize GIF playback
  gif.begin(LITTLE_ENDIAN_PIXELS);
}

void loop() {
  if (shouldReboot) {
    rebootESP("Web Admin Initiated Reboot");
  }

  while (1) { // Run forever
    root = FILESYSTEM.open(gifDir); // Open the root directory
    if (root) {
      // Check if a new GIF is requested via the play button
      if (!requestedGifPath.isEmpty()) {
        // Play the requested GIF and set it as the current GIF
        currentGifPath = requestedGifPath; // Update the current GIF path
        requestedGifPath = ""; // Clear the requested path

        // Find and play the requested GIF
        while (gifFile = root.openNextFile()) {
          if (String(gifFile.path()) == currentGifPath) {
            break;
          }
        }

        if (gifFile) {
          // Only play if it's a GIF file
          String fileName = String(gifFile.path());
          if (isGifFile(fileName)) {
            // Play the requested GIF
            memset(filePath, 0x0, sizeof(filePath));
            strcpy(filePath, gifFile.path());
            ShowGIF(filePath);

            // If looping is enabled, continue looping the requested GIF
            if (loopGifEnabled) {
              continue; // Restart the loop for the same GIF
            }
          }
        }
      } else if (!currentGifPath.isEmpty()) {
        // Resume from the last GIF
        while (gifFile = root.openNextFile()) {
          if (String(gifFile.path()) == currentGifPath) {
            break;
          }
        }
      } else {
        // Open the first GIF file in the directory
        do {
          gifFile = root.openNextFile();
        } while (gifFile && !isGifFile(String(gifFile.path())));
      }

      while (gifFile) {
        if (!gifFile.isDirectory()) { // Play the file if it's not a directory
          String fileName = String(gifFile.path());
          // Only play files with .gif extension
          if (fileName.endsWith(".gif") || fileName.endsWith(".GIF")) {
            memset(filePath, 0x0, sizeof(filePath));
            strcpy(filePath, gifFile.path());
            currentGifPath = String(filePath); // Save the current GIF path

            // Show the GIF
            ShowGIF(filePath);

            // If looping is enabled, continue playing the same GIF
            if (loopGifEnabled) {
              continue; // Restart the loop for the same GIF
            }
          }
        }

        if (!loopGifEnabled) {
          // If looping is disabled, move to the next GIF file
          gifFile.close();      // Close the current GIF file

          // Find the next GIF file
          do {
            gifFile = root.openNextFile();
          } while (gifFile && !String(gifFile.path()).endsWith(".gif") && !String(gifFile.path()).endsWith(".GIF"));

          // If no more GIF files, reset to the first GIF file
          if (!gifFile) {
            root.close(); // Close the root directory
            root = FILESYSTEM.open(gifDir); // Reopen the root directory
            do {
              gifFile = root.openNextFile();
            } while (gifFile && !String(gifFile.path()).endsWith(".gif") && !String(gifFile.path()).endsWith(".GIF"));
          }
        } else {
          break; // Exit the loop if looping is disabled
        }
      }

      root.close(); // Close the root directory
    }

    delay(10); // Pause before restarting
  }
}