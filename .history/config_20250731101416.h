#ifndef CONFIG_H
#define CONFIG_H

#include <Arduino.h>

// HTTP method definitions for ESP_Async_WebServer
// These must be defined before including ESPAsyncWebServer.h
typedef enum {
  HTTP_GET     = 0b00000001,
  HTTP_POST    = 0b00000010,
  HTTP_DELETE  = 0b00000100,
  HTTP_PUT     = 0b00001000,
  HTTP_PATCH   = 0b00010000,
  HTTP_HEAD    = 0b00100000,
  HTTP_OPTIONS = 0b01000000,
  HTTP_ANY     = 0b01111111,
} WebRequestMethod;

// Firmware and filesystem constants
#define FIRMWARE_VERSION "v0.4.5b"
#define FILESYSTEM SPIFFS

// Hardware pin definitions
#define A_PIN   22 
#define B_PIN   32 
#define C_PIN   33
#define D_PIN   17 
#define E_PIN   21 

// Panel configuration
#define PANEL_RES_X 64      
#define PANEL_RES_Y 64     
#define PANEL_CHAIN 1      

// Default configuration values
extern const String default_ssid;
extern const String default_wifipassword;
extern const String default_httpuser;
extern const String default_httppassword;
extern const int default_webserverporthttp;
extern const char* ntpServer;
extern const char* PARAM_INPUT;
extern const long gmtOffset_sec;
extern const int daylightOffset_sec;
extern const int maxGIFsPerPage;

// Configuration structure
struct Config {
  String ssid;
  String wifipassword;
  String httpuser;
  String httppassword;
  int webserverporthttp;
};

// Global configuration variables
extern Config config;
extern bool shouldReboot;

// Configuration functions
void initializeConfig();
void rebootESP(String message);

#endif