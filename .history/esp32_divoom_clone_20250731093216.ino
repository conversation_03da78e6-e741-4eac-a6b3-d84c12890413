//By mzashh https://github.com/mzashh
//Organized version with separate header files

#define FIRMWARE_VERSION "v0.4.5b"
#define FILESYSTEM SPIFFS

#include <SPIFFS.h>
#include <AnimatedGIF.h>
#include <ESP32-HUB75-MatrixPanel-I2S-DMA.h>
#include <WiFi.h>
#include <AsyncTCP.h>
#include <GFX_Layer.hpp>
#include <ESPAsyncWebServer.h>
#include "time.h"

#include "config.h"
#include "display.h"
#include "gif.h" 
#include "webserver.h"

void setup() {
  Serial.begin(115200);
  Serial.print("Firmware: "); Serial.println(FIRMWARE_VERSION);
  Serial.println("Booting ...");

  // Initialize filesystem
  Serial.println("Mounting SPIFFS ...");
  if (!SPIFFS.begin(true)) {
    Serial.println("ERROR: Cannot mount SPIFFS, Rebooting");
    rebootESP("ERROR: Cannot mount SPIFFS, Rebooting");
  }

  // Initialize display
  setupDisplay();

  // Print filesystem info
  Serial.print("Flash Free: "); Serial.println(humanReadableSize((SPIFFS.totalBytes() - SPIFFS.usedBytes())));
  Serial.print("Flash Used: "); Serial.println(humanReadableSize(SPIFFS.usedBytes()));
  Serial.print("Flash Total: "); Serial.println(humanReadableSize(SPIFFS.totalBytes()));

  // Initialize config
  initializeConfig();

  // Setup WiFi
  setupWiFi();

  // Setup web server
  setupWebServer();

  // Setup time
  configTime(gmtOffset_sec, daylightOffset_sec, ntpServer);

  // Show startup info
  showStartupInfo();

  // Initialize GIF playback
  gif.begin(LITTLE_ENDIAN_PIXELS);
}

void loop() {
  if (shouldReboot) {
    rebootESP("Web Admin Initiated Reboot");
  }

  while (1) { // Run forever
    root = FILESYSTEM.open(gifDir); // Open the root directory
    if (root) {
      // Check if a new GIF is requested via the play button
      if (!requestedGifPath.isEmpty()) {
        // Play the requested GIF and set it as the current GIF
        currentGifPath = requestedGifPath; // Update the current GIF path
        requestedGifPath = ""; // Clear the requested path

        // Find and play the requested GIF
        while (gifFile = root.openNextFile()) {
          if (String(gifFile.path()) == currentGifPath) {
            break;
          }
        }

        if (gifFile) {
          // Play the requested GIF
          memset(filePath, 0x0, sizeof(filePath));
          strcpy(filePath, gifFile.path());
          ShowGIF(filePath);

          // If looping is enabled, continue looping the requested GIF
          if (loopGifEnabled) {
            continue; // Restart the loop for the same GIF
          }
        }
      } else if (!currentGifPath.isEmpty()) {
        // Resume from the last GIF
        while (gifFile = root.openNextFile()) {
          if (String(gifFile.path()) == currentGifPath) {
            break;
          }
        }
      } else {
        gifFile = root.openNextFile(); // Open the first file in the directory
      }

      while (gifFile) {
        if (!gifFile.isDirectory()) { // Play the file if it's not a directory
          memset(filePath, 0x0, sizeof(filePath));
          strcpy(filePath, gifFile.path());
          currentGifPath = String(filePath); // Save the current GIF path

          // Show the GIF
          ShowGIF(filePath);

          // If looping is enabled, continue playing the same GIF
          if (loopGifEnabled) {
            continue; // Restart the loop for the same GIF
          }
        }

        if (!loopGifEnabled) {
          // If looping is disabled, move to the next GIF
          gifFile.close();      // Close the current GIF file
          gifFile = root.openNextFile(); // Open the next file

          // If no more files, reset to the first file
          if (!gifFile) {
            root.close(); // Close the root directory
            root = FILESYSTEM.open(gifDir); // Reopen the root directory
            gifFile = root.openNextFile(); // Start from the first file again
          }
        } else {
          break; // Exit the loop if looping is disabled
        }
      }

      root.close(); // Close the root directory
    }

    delay(10); // Pause before restarting
  }
}