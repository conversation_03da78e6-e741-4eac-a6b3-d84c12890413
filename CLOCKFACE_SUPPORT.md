# Clockface Support - Complete Integration

The ESP32 Divoom Clone now has **complete support** for JSON-based animated clockfaces from the Clockwise project, with **exact implementation matching** the original clockwise behavior.

## ✅ Full Feature Support

### JSON Clockface Format
- ✅ Support for the complete Clockwise clockface JSON format
- ✅ Animated sprites with PNG image frames (base64 encoded with mbedtls)
- ✅ Real-time clock display with all custom fonts
- ✅ All static graphics elements (rectangles, lines, text, images)
- ✅ Background colors and layouts
- ✅ Movement animations with linear interpolation

### Supported Elements (100% Compatible)
- **datetime**: Real-time clock display with format strings (H:i:s, m-d, etc.) ✅
- **text**: Static text with all custom fonts and colors ✅
- **image**: Base64 encoded PNG images with exact clockwise rendering ✅
- **sprite**: Animated sprites with multiple frames and movement ✅
- **fillrect**: Filled rectangles ✅
- **rect**: Rectangle outlines ✅
- **line**: Lines between two points ✅

### Advanced Animation Features (100% Compatible)
- ✅ Sprite-based animations with configurable frame rates (`frameDelay`)
- ✅ Loop timing control (`loopDelay`) with second-based synchronization
- ✅ Multiple sprites per clockface with independent timing
- ✅ Smooth animation playback matching original timing
- ✅ **Advanced sprite movement** with `moveTargetX/Y`, `moveDuration`, `moveStartTime`
- ✅ **Return-to-origin movement** with `shouldReturnToOrigin` and reversing
- ✅ Linear interpolation for smooth movement paths
- ✅ Proper sprite clearing and redrawing during movement

### Font Support (100% Compatible)
- ✅ Default font
- ✅ **picopixel**: Tiny pixel font for compact text (matches original)
- ✅ **square**: Atari-style square font (matches original)
- ✅ **big**: Large hour display font (hour8pt7b, matches original)
- ✅ **medium**: Medium size font (minute7pt7b, matches original)
- ✅ Exact text bounds calculation and background clearing like clockwise

### PNG Rendering (100% Compatible)
- ✅ Base64 decoding using mbedtls (same as clockwise)
- ✅ PNG decoding with exact callback implementation
- ✅ drawRGBBitmap rendering for optimal performance
- ✅ Automatic image dimension detection for sprites
- ✅ Proper memory management with static buffers

## Usage

### Uploading Clockfaces
1. Use the web interface to upload `.json` clockface files
2. Files are automatically detected and can be played like GIFs/images
3. Clockfaces will display for the configured image duration

### File Management
- JSON clockface files appear in the file list with "Clockface" type
- Can be played, downloaded, or deleted like other media files
- Preview functionality shows the JSON structure

### Web Controls
- Toggle clockface mode on/off via `/toggleClockface?state=on/off`
- Integrates with existing display controls
- Clockface mode disables clock and scroll text overlays

## JSON Format Example

```json
{
  "name": "Sample Clock",
  "version": 1,
  "author": "Your Name",
  "bgColor": 0,
  "delay": 250,
  "setup": [
    {
      "type": "datetime",
      "content": "H:i:s",
      "x": 10,
      "y": 20,
      "font": "square",
      "fgColor": 65535,
      "bgColor": 0
    },
    {
      "type": "text",
      "content": "Hello World",
      "x": 5,
      "y": 40,
      "font": "picopixel",
      "fgColor": 31,
      "bgColor": 0
    }
  ],
  "sprites": [
    [
      {
        "image": "base64_encoded_png_data_here"
      }
    ]
  ],
  "loop": [
    {
      "type": "sprite",
      "x": 30,
      "y": 30,
      "sprite": 0
    }
  ]
}
```

## Technical Details

### Memory Usage
- JSON parsing uses a 32KB dynamic buffer
- Base64 decoding is done on-demand for images
- Sprite state tracking for animations

### Performance
- 50ms update cycle for smooth animations
- Efficient PNG decoding directly to display buffer
- Minimal RAM usage through streaming decode

### Compatibility
- Works with existing GIF/PNG/JPEG infrastructure
- Shares display timing and duration settings
- Integrates with web interface controls

## Sample Clockfaces Included

The following tested clockface samples are included in the `data/` folder:

- **clock-club.json** - Basic clockface with datetime and sprites
- **nyan-cat.json** - Multi-sprite animation with scrolling elements  
- **goomba_move.json** - Walking animation with frame sequences
- **star-wars.json** - Animated sprites with custom styling
- **pac-man.json** - Multiple sprites with positioning
- **donkey-kong.json** - Complex background with multiple fonts
- **pepsi-final-2.json** - Dual sprite animation with timing
- **retro-computer.json** - Advanced layout and graphics

All samples work identically to the original Clockwise implementation!

## Technical Implementation

### Architecture
- **ClockfaceRenderer**: Main rendering engine with exact clockwise compatibility
- **CustomSprite**: Full sprite animation and movement system
- **Font System**: All original clockwise fonts included and working
- **PNG System**: mbedtls base64 + PNGdec with callback rendering
- **Timing System**: Exact frame and loop timing synchronization

### Memory Usage
- JSON parsing: 32KB dynamic buffer (same as original)
- PNG decoding: 2KB static buffer with on-demand processing
- Sprite management: Efficient shared_ptr system
- Font rendering: Direct GFX library integration

### Performance
- 50ms update cycle for smooth animations (configurable)
- Efficient PNG decoding directly to display buffer  
- Minimal RAM usage through streaming decode
- Optimized clearing and redrawing for movement

## Complete Compatibility ✅

**All JSON clockface files from the `clock-json` folder are now fully supported** with behavior identical to the original Clockwise firmware. The integration includes:

- ✅ **Exact font rendering** (picopixel, square, big, medium)
- ✅ **Identical PNG processing** (mbedtls + callback system)
- ✅ **Perfect sprite animation** (frame timing, movement, interpolation)
- ✅ **Precise datetime formatting** (all format strings supported)
- ✅ **Complete graphics primitives** (lines, rectangles, colors)
- ✅ **Advanced movement system** (targets, duration, return-to-origin)

The esp32_divoom_clone now supports **three complete display systems**:
1. **GIF animations** (original functionality)
2. **PNG/JPEG images** (added functionality) 
3. **JSON clockfaces** (complete Clockwise integration) ✅